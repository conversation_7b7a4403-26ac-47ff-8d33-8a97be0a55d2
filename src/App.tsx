
import './App.css'
import { Suspense } from "react";
import { Outlet } from 'react-router-dom'
import Navbar from './components/navbar'
import Footer from './components/footer'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import ErrorBoundary from './components/errors/errorBoundary'
import { Loader2 } from 'lucide-react'
import { NetworkError } from './components/errors/networkError';
import { useOnline } from '@uiw/react-use-online';
const queryClient = new QueryClient()

function App() {
  const isOnline = useOnline();

  if (!isOnline) {
    return <NetworkError />;
  }

  return (
    <QueryClientProvider client={queryClient}>
        <ErrorBoundary>
        <Suspense fallback={<div className='flex justify-center items-center h-screen'>
          <Loader2 className='w-10 h-10 animate-spin' />
        </div>}>
      <div className='flex flex-col min-h-screen'>
        <Navbar/>
        <Outlet /> 
      <Footer/>
      </div>
      </Suspense>
      </ErrorBoundary>
    </QueryClientProvider>
  );
}

export default App
