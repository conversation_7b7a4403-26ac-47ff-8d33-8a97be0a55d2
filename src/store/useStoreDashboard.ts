//store for filters data 
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
interface FiltersState {
  integrationType: string[];
  departments: string[];
  schemes: string[];
  year: string[];
  initialized: boolean;
  setIntegrationType: (integrationType: string[]) => void;
  setDepartments: (departments: string[]) => void;
  setSchemes: (schemes: string[]) => void;
  resetFilters: () => void;
  setInitialized: (initialized: boolean) => void;
}
export const useStoreDashboard = create<FiltersState>()(
  persist(
    (set) => ({
      integrationType: [],
      departments: [],
      schemes: [],
      year: [],
      initialized: false,
      setIntegrationType: (integrationType) => set({ integrationType }),
      setDepartments: (departments) => set({ departments }),
      setInitialized: (initialized) => set({ initialized }),
      setSchemes: (schemes) => set({ schemes }),
      resetFilters: () => set({ integrationType: [], departments: [], schemes: []}),
    }),
    {
      name: 'filters-storage',
    }
  )
);
