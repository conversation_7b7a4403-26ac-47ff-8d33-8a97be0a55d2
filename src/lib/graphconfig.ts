import type { ChartConfig } from "@/components/ui/chart";

export const skillTrainingConfig = {
    enrolledInBatch: {
      label: "Enrolled In Batch",
      color: "#8ab8f5",
    },
    enrolledWithoutBatch: {
      label: "Enrolled Without Batch",
      color: "#f87171",
    }
  } satisfies ChartConfig 



  export const duplicateAnalysisConfig = {
    unique: {
      label: "Unique",
      color: "#8ab8f5",
    },
    multiple: {
      label: "Multiple",
      color: "#f87171",
    }
  } satisfies ChartConfig 


  export const genderDistributionConfig = {
    male: {
      label: "Male",
      color: "#8ab8f5",
    },
    female: {
      label: "Female",
      color: "#f87171",
    },
    transgender: {
      label: "Transgender",
      color: "#f87171",
    },
    notSpecified: {
      label: "Not Specified",
      color: "#f87171",
    }
  } satisfies ChartConfig 



  export const religionDistributionConfig = {
    hindu: {
      label: "Hindu",
      color: "#8ab8f5",
    },
    muslim: {
      label: "Muslim",
      color: "#f87171",
    },
    christian: {
      label: "Sikh",
      color: "#f87171",
    },
    other: {
      label: "Other",
      color: "#f87171",
    }
  } satisfies ChartConfig  


  export const AadhaarStatusDistributionConfig = {
    withAadhaar: {
      label: "With Aadhaar",
      color: "#8ab8f5",
    },
    withoutAadhaar: {
      label: "Without Aadhaar",
      color: "#f0b100",
    },
    duplicates: {
      label: "Duplicates",
      color: "#f87171",
    }
  } satisfies ChartConfig 