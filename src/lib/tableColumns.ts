import type { TableColumn } from "@/components/table/table";
import type { candidateDetailsResponse } from "./types";

export const candidateDetailsColumns: TableColumn<candidateDetailsResponse>[] = [
    { key: "id", label: "SL No" },
    { key: "vsCandidateName", label: "Candidate Name" },
    { key: "vsGender", label: "Gender" },
    { key: "caste", label: "Caste" },

    { key: "vsQualification", label: "Qualification" },
    { key: "departmentName", label: "Department" },
];

export const TrainingCentersColumns: TableColumn<candidateDetailsResponse>[] = [
    { key: "id", label: "SL No" },
    { key: "tcName", label: "Training Center Name" },
    { key: "vsDistrictName", label: "Center District" },
    {key:"longitude",label :"Center Geo Tag"},
    {key:"totalEnrolled_count", label:"Total Enrolled Candidate"},
    {key:"totalEertifiedCand", label:"Total Certified Candidate"},
    {key:"totalElacedCand", label:"Total Placed Candidate"},
    { key: "tpName", label: "Training Partner Name" },
 
    { key: "departmentName", label: "Department" },
];


export const TrainingPartnersColumns: TableColumn<candidateDetailsResponse>[] = [
    { key: "id", label: "SL No" },
    { key: "vsTpName", label: "Training Partner Name" },
    { key: "address", label: "Training Partner Address" },
    {key:"totalCandidate", label:"Total Enrolled Candidate"},
    {key:"totalCertified", label:"Total Certified Candidate"},
    {key:"totalPlaced", label:"Total Placed Candidate"},
    { key: "vsDepartmentName", label: "Department" },
]



export const assessmentColumns = [
    {key:"id", label:"SL No "},
    { key: "iBatchNumber", label: "Batch Number" },
    { key: "candidateId", label: "Candidate " },
    { key: "assessmentStatus", label: "Assessment Status" },
    { key: "vsResult", label: "Result" },
    { key: "tcName", label: "Training Center " },
   
    { key: "vsDepartmentName", label: "Department Name" }
  
]



// export const batchesColumns = [
//     {
//         key: "batchId",
//         label: "Batch ID",
//     },
//     {
//         key: "trainingCenter",
//         label: "Training Center",
//     },
//     {
//         key: "jobRole",
//         label: "Job Role",
//     },
//     {
//         key: "trainerName",
//         label: "Trainer name",
//     },
//     {
//         key: "batchSizeEnrolled",
//         label: "Batch Size Enrolled",
//     },
//     {
//         key: "batchStartDate",
//         label: "Batch Start Date",
//     },
//     {
//         key: "batchEndDate",
//         label: "Batch End Date",
//     },

// ]


export const coursesColumns = [

   
    {
        key: "vsCourseName",
        label: "Job Role Name",
    },
    {
        key:"vsCourseCode",
        label :"QPNOS Code"
    },
    {
        key: "startDate",
        label: "Start Date",
    },
    {
        key: "endDate",
        label: "End Date",
    },
    {
        key:"vsSectorName",
        label :"Sector Name"
    },
    {
        key:"totalEnrolled",
        label :"Total Enrolled Candidate"
    },
    {
        key:"totalCertified",
        label :"Total Certified Candidate"
    },
    {
        key:"totalPlaced",
        label :"Total Placed Candidate"
    },
   
    {
        key:"department_name",
        label :"Department Name"
    }

]

export const trainerColumns = [
    { key: "id", label: "SL No" },
    { key: "vsTrainerName", label: "Trainer Name" },
 
        {key:"courseName", label:"Course Name"},
   
    { key: "vsTcName", label: "Training Center Name" },
   
    { key: "vsDepartmentName", label: "Department Name" }
  ];
  


export const placementColumns = [
    { key: "id", label: "SL No " },
    { key: "iBatchNumber", label: "Batch ID" },
    {key :"candidateId", label:"Candidate"},
    { key: "vsPlacementType", label: "Placement Type" },

    { key: "vsDepartmentName", label: "Department " },
 



];

export const summaryReportColumns = [
 // SL No	Scheme Name	Financial Year	Total Target	Total Job Role Count	Male Count	Female Count	General Count	SC Count	ST Count	OBC Count	Minority Count	Tea Tribe Count	PwD Count	Total Certified Candidate	Total Placed Candidate
    
    {key:"id", label : "SL NO "},
    {key :"departmentName", label:"Department Name", headerClassName:"w-1/12"},
    { key: "vsSchemeName", label: "Scheme Name" },
    { key: "dtFinancialYear", label: "Financial Year" },
    { key: "itotalTarget", label: "Total Target" },
    { key: "iTotalJobRoleCount", label: "Total Job Role " },
    { key: "iMaleCount", label: "Total Male Candidates " },
    { key: "iFemaleCount", label: "Total Female Candidates " },
    { key: "iOtherCount", label: "Total Other Candidates " },
    { key: "itotalTrainingCandidate", label: "Total Trained Candidates" },
    { key: "itotalCertifiedCandidate", label: "Total Certified Candidates" },
    { key: "itotalPlacedCandidate", label: "Total Placed Candidates" },
    { key: "iGeneralCount", label: "Total General Candidates " },
    { key: "iScCount", label: "Total SC Candidates " },
    { key: "iStHCount", label: "Total ST (Hills) Candidates " },
    { key: "iStPCount", label: "Total ST (Plains) Candidates " },
    { key: "iObcCount", label: "Total OBC Candidates " },

    { key: "iMinorityCount", label: "Total Minority Candidates " },
    { key: "iTeaTribeCount", label: "Total Tea Tribe Candidates " },
    { key: "iPwdCount", label: "Total PWD Candidates " },
   
    { key: "iTotalCandidateCount", label: "Total Candidates " },
 

    
]


export const DashBoardSummaryColumns = [
    {key:"id", label:"SL No"},
    {key:"department", label:"Department Name"},
    {key:"totalSchemeCount", label:"Scheme"},
    {key:"totalCandidate", label:"Total Candidate"},
    {key:"totalTpCount", label:"Total Training Partner "},
    {key:"totalTcCount", label:"Total Training Center"},
    {key:"totalTrainer", label:"Total Trainer"},
 
]


export  const schemeDetailsColumns = [
    {key:"id", label:"SL No"},
    {key:"department", label:"Department Name"},
    {key:"schemeName", label:"Scheme Name"},
    {key:"courseCount", label:"Total Job Role"},
  
    {key:"tpCount", label:"Total Training Partner"},
    {key:"tcCount", label:"Total Training Center"},

    {key:"enrolledCount", label:"Total Enrolled Candidate"},
    {key :"certifiedCount" ,label :"Total Certified Candidate"},
    {key :"placedCount" ,label :"Total Placed Candidate"}
]



export const BatchSummaryColumns = [
    {key:"id", label:"SL No"},
    {key:"department", label:"Department Name"},
    {key:"scheme", label:"Scheme "},
    {key:"totalCandidate", label:"Total Candidate"},
    {key:"totalTpCount", label:"Total Training Partner "},
    {key:"totalTcCount",label :"Total Training Center"},
    {key:"totalTrainer" ,label :"Total Trainer"},

]
export const sectorColumns = [
    {key:"id", label:"SL No"},
    {key:"vsSectorName", label:"Sector Name"},
    {key:"course_count",label:"Job Role Count"},
    {key:"tc_count",label:"Training Center Count"},
    {key:"enrolled_count",label:"Enrolled Count"},
    {key:"certified_count",label:"Certified Count"},
    {key:"placed_count",label:"Placed Count"},
    {key:"scheme_count",label:"Scheme Count"},
    {key:"department_count",label:"Total Department Count"},
  
]


export const batchColumns = [
    {key:"id", label:"SL No"},
    {key:"batchStartDate", label:"Batch Start Date"},
    {key:"batchEndDate", label:"Batch End Date"},
    {key:"tc", label:"Training Center"},
    {key:"vsCourseName", label:"Job Role"},
    {key:"vsSchemeName", label:"Scheme "},
    {key:"trainnerName", label:"Trainer "},

    {key:"departmentName", label:"Department "},
    {key:"enrolled_count", label:"Enrolled Count"},
    {key:"certified_count", label:"Certified Count"},
    {key:"placed_count", label:"Placed Count"},
    

]