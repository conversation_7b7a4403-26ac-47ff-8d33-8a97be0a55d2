import type { ChartConfig } from "@/components/ui/chart";

export interface DashboardCount {
    sectors: number;
    courses: number;
    tps: number;
    tcs: number;
    batchs: number;
    candidates: number;
    trainers: number;
    assessors: number;
    assessements: number;
    placements: number;
    summaryReport: number;
    candidateBatchWiseCount :number;
  }
  export interface cardsCountPayload {
    departmentId?: string[];
    schemeName?: string[];
    integrationType?: string[];
  }
  export interface DashboardStatsResponse {
  
    data?: {
      count: DashboardCount;
    };
  }
  export interface CardItem {
    title: string;
    count: number | string | undefined;   
    icon: React.ReactNode;
    to: string;  
  }
  interface PieChartData {
    [key: string]: string | number
    fill: string
  }
  
  export interface ReusablePieChartProps {
    data: PieChartData[]
    config: ChartConfig
    title: string
    description?: string
    dataKey: string
    nameKey: string
    innerRadius?: number
    outerRadius?: number
    className?: string
    showTrend?: boolean
    trendValue?: number
    footerText?: string
    maxHeight?: number
  
  }
  export interface candidateDetailsPayload { 
    departmentId: string[];
    card: string;
    integrationType: number[];
    schemeName: string[];
    take: number;
    skip: number;
    gender:string
    entryType :string
    qualification:string
    search:string,
    data_type:string
    }
  
  export interface candidateDetailsResponse {
     
    pages: number;
    total: number;
    values: any;
    id: string;
    name: string;
    email: string;
    vsCandidateName: string;
    vsGender: string;
    caste: string;
    vsMobile: string;
    vsQualification: { vsQualification: string }[];
    departmentName: string;
    vsAadhaar: string;
    vsAadhaarStatus: string;
    vsAadhaarStatusDate: string;
    data: {
      
    }
    list:    any[]
  }


export interface FilterOption {
    value: string;
    label: string;
    checked: boolean;
  }
  
  export interface FilterState {
    entryType: FilterOption[];
    qualification: FilterOption[];
    gender: FilterOption[];
    batch: FilterOption[];
  } 
  


export interface TrainingCenterFilterState {
    entryType: string[];
    district: string; 
    departmentId: string[];
    card: string;
    integrationType: number[];
    schemeName: string[];
    search: string; 
    take: number;
    skip: number;
  }
  
  export interface TrainingCenter {
    rank: number;
    name: string;
  
    coordinates: string;
    trainingPartner: string;
    department: string;
    certified: number;
    data: any;    
  }


  export interface DashboardPayload {
    startDate?: string;
    endDate?: string;
    department?: string[];
    scheme?: string[];
    integrationType?: string[];
  } 

  export interface TrainingPartnerFilterState {
    departmentId: string[];
    card: string[];
    integrationType: number[];
    schemeName: string[];
    entryType: string[];
    district: string[];
    take: number;
    skip: number;
    search: string;
  }
  