import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'

import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import {Hero} from './pages/Hero.tsx';
import App from './App.tsx';
import Assessment from './pages/Assessment.tsx';
import SummaryReport from './pages/SummaryReport.tsx';
import Batches from './pages/Batches.tsx';
import Candidates from './pages/Candidates.tsx';
import Courses from './pages/Courses.tsx';
import Placement from './pages/Placement.tsx';
import Sectors from './pages/Sectors.tsx';
import Trainers from './pages/Trainers.tsx';
// import TrainingCenterDashboard from './pages/TrainingCenter.tsx';
import TrainingPartner from './pages/TrainingPartner.tsx';
import TrainingCenterDashboard from './pages/TrainingCenter.tsx';
import SchemeDetails from './pages/SchemeDetails.tsx';


const router = createBrowserRouter([
  {
    path: '/',
    element: <App />, 
    children: [
      {
        index: true,
        element: <Hero />
      },
      {
        path: '/assessment',
        element: <Assessment />
      },
      {
        path: '/summary-report',
        element: <SummaryReport />
      },
      {
        path: '/batches',
        element: <Batches />
      },
      {
        path: '/candidates',
        element: <Candidates />
      },
      {
        path: '/courses',
        element: <Courses />
      },
      {
        path: '/placement',
        element: <Placement />
      },
      {
        path: '/sectors',
        element: <Sectors />
      },
      {
        path: '/trainers',    
        element: <Trainers />
      },
      {
        path: '/training-centers',
        element: <TrainingCenterDashboard />
      },
      {
        path: '/training-partners',
        element: <TrainingPartner />
      },
      {
        path: '/schemesbydepartment/:id',
        element: <SchemeDetails />
      }
    ]
  }
]);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <RouterProvider router={router} />
  </StrictMode>,
)
