import React, { useMemo, useState } from 'react';
import { <PERSON>Map, <PERSON>er, InfoWindow, useJsApiLoader } from '@react-google-maps/api';
import { LucideLoader } from 'lucide-react';

interface RawLocation {
  latitute?: string;
  longitude?: string;
  name  : string;
  address: string;
}

interface Location {
  lat: number;
  lng: number;
  name: string;
  address: string;
}

const isValidCoordinate = (num: number): boolean => {
  return !isNaN(num) && isFinite(num);
};

export const MapView: React.FC<{ locations: RawLocation[] }> = ({ locations: rawLocations }) => {
  const [hoveredMarker, setHoveredMarker] = useState<number | null>(null);

  const center = useMemo(() => ({ lat: 26.15, lng: 92.5 }), []);

  const API = import.meta.env.VITE_GOOGLE_MAP_API_KEY;

  const processedLocations: Location[] = useMemo(() => {
    if (!Array.isArray(rawLocations)) return [];

    return rawLocations
      .map((item) => {
        const lat = parseFloat(item.latitute ?? '0');
        const lng = parseFloat(item.longitude ?? '0');

        if (isValidCoordinate(lat) && isValidCoordinate(lng)) {
          return {
            lat,
            lng,
            name: item.name,
            address: item.address,
          };
        }

        return null;
      })
      .filter((item): item is Location => item !== null);
  }, [rawLocations]);

  const { isLoaded } = useJsApiLoader({
    googleMapsApiKey: API || '',
  });

  if (!isLoaded) {
    return (
      <div className="w-full h-[25rem] flex items-center justify-center">
        <LucideLoader className="animate-spin h-6 w-6 text-gray-500" />
        <span className="ml-2 text-gray-600">Loading map...</span>
      </div>
    );
  }

  return (
    <div className="w-full h-[25rem] rounded-lg overflow-hidden shadow-lg border border-gray-200">
      <GoogleMap
        mapContainerStyle={{ width: '100%', height: '100%' }}
        center={center}
        zoom={8}
      >
        {processedLocations.map((loc, index) => (
          <Marker
            key={index}
            position={{ lat: loc.lat, lng: loc.lng }}
            icon={{
              url: 'https://cdn-icons-png.flaticon.com/128/2776/2776063.png',
              scaledSize: new window.google.maps.Size(32, 32),
            }}
            onMouseOver={() => setHoveredMarker(index)}
            onMouseOut={() => setHoveredMarker(null)}
          >
            {hoveredMarker === index && (
              <InfoWindow position={{ lat: loc.lat, lng: loc.lng }}>
                <div style={{ maxWidth: 200 }}>
                  <h3 className="font-bold mb-1">{loc.name}</h3>
                  <p className="text-xs text-gray-600">{loc.address}</p>
                  <p className="text-[10px] text-gray-400 mt-1">
                    Lat: {loc.lat.toFixed(6)}, Lng: {loc.lng.toFixed(6)}
                  </p>
                </div>
              </InfoWindow>
            )}
          </Marker>
        ))}
      </GoogleMap>
    </div>
  );
};
