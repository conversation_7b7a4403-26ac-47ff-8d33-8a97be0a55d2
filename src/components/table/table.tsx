import * as React from "react";
import { cn } from "@/lib/utils";

export type TableColumn<T> = {
  key: keyof T | string;
  label: React.ReactNode;
  className?: string;
  headerClassName?: string;
  cellRender?: (value: any, row: T, rowIndex: number) => React.ReactNode;
};

export type TableProps<T extends Record<string, any>> = {
  columns: TableColumn<T>[];
  data: T[];
  className?: string;
  headerClassName?: string;
  rowClassName?: (row: T, rowIndex: number) => string;
  emptyMessage?: React.ReactNode;
};

export function Table<T extends Record<string, any>>({
  columns,
  data,
  className,
  headerClassName,
  rowClassName,
  emptyMessage = "No data available.",
}: TableProps<T>) {
  return (
    <div className={cn("relative overflow-x-auto overflow-y-auto border rounded-lg", className)}>
      <table className="w-full text-xs text-left text-muted-foreground">
        <thead className={cn("uppercase bg-muted/50 align-top ", headerClassName)}>
          <tr>
            {columns.map((col, idx) => (
              <th
                key={String(col.key) + idx}
                className={cn("px-4 py-3 font-bold text-sm", col.headerClassName)}
                scope="col"
              >
                {col.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.length === 0 ? (
            <tr>
              <td colSpan={columns.length} className="px-4 py-6 text-center text-muted-foreground">
                {emptyMessage}
              </td>
            </tr>
          ) : (
            data.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className={cn("bg-background border-b hover:bg-muted/30 transition-colors", rowClassName?.(row, rowIndex))}
              >
                {columns.map((col, colIndex) => (
                  <td key={String(col.key) + colIndex} className={cn("px-4 py-3", col.className)}>
                    {col.cellRender
                      ? col.cellRender(row[col.key as keyof T], row, rowIndex)
                      : row[col.key as keyof T]}
                  </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
}

export default Table;
