  import React from 'react';
  import CardsCount from './cards';
  import {Demo<PERSON>hart} from './graphs/demo-chart';
  import PieCharts from './graphs/pie-charts';
  import { Link } from 'react-router-dom';
  import { skillTrainingConfig } from '@/lib/graphconfig';
  import { useStoreDashboard } from '@/store/useStoreDashboard';
  import {  DashBoardSummaryColumns } from '@/lib/tableColumns';
  import Table from './table/table';
  import { useFetchQuery } from '@/hooks/useFetchQuery';
  import type { cardsCountPayload, DashboardStatsResponse } from '@/lib/types';
  import { Loader } from 'lucide-react';
import DashFilter from './filters/dash-filter';

  const Dashboard: React.FC = ( ) => {
    const departments = useStoreDashboard(state => state.departments);
    // const schemes = useStoreDashboard(state => state.schemes);
    const integrationType = useStoreDashboard(state => state.integrationType);
    console.log(integrationType)
    const columns = DashBoardSummaryColumns.map(col =>
      col.key ==="totalSchemeCount"
        ? {
            ...col,
            cellRender: (value: any, row: any) => 
              value === 0 ? <span className="text-gray-500">-</span> :
                <Link
                to={`/schemesbydepartment/${row.departmentShortName}`}
                className="text-sky-600 hover:underline cursor-pointer font-bold"
              >
                {value} (click me)
              </Link>
              
            
          }
        : col.key ==="totalCandidate"
        ? {
            ...col,
            cellRender: (value: any) => 
              value === 0 ? <span className="text-gray-500">-</span> :
                <Link
                to={'/candidates'}
                className="text-sky-600 hover:underline cursor-pointer font-bold"
              >
                {value} (click me)
              </Link>
              
            
          }
        : col
    );


    
    const shouldFetch = departments.length > 0 && integrationType.length > 0;

    const { data: cardsCount, isLoading } = useFetchQuery<DashboardStatsResponse, cardsCountPayload>(
      'dashboardCount',
      import.meta.env.VITE_API_URL + '/dashboard/home',
      {
        departmentId: departments,
        schemeName: [], 
        integrationType: integrationType 
      },
      { enabled: shouldFetch }
    );
    //@ts-ignore
    const data = cardsCount?.data?.candidateBatchYearWise?.map((item: any) => ({
      year: item.year,
      male: item.maleCount,
      female: item.femaleCount,
      notSpecified: item.notSpecified,
      total: item.totalCount,
      totalTarget: item.totalTarget ,
    }))
    
    const maleData = data?.map((item: any) => item.male) || [12,13,14,15,16,17,18,19,20,21];
    const femaleData = data?.map((item: any) => item.female) || [12,13,14,15,16,17,18,19,20,21];
    const notSData = data?.map((item: any) => item.notSpecified) || [12,13,14,15,16,17,18,19,20,21];
    const trans= data?.map((item: any) => item.otherCount) || [12,13,14,15,16,17,18,19,20,21];

    //@ts-ignore

    const enrolledWithoutBatch =  cardsCount?.data?.candidateBatchWiseCount?.map((item: any) => item.totalWithoutBatch) || 92;
    //@ts-ignore
    const enrolledInBatch =  cardsCount?.data?.candidateBatchWiseCount?.map((item: any) => item.totalWithBatch) || 92;

  
    console.log(".EnrollinBatch", enrolledInBatch)
    console.log(".EnrollwithoutBatch", enrolledWithoutBatch)


    const { data: batchSummary } = useFetchQuery(
      'batchSummary',
      import.meta.env.VITE_API_URL + '/dashboard/all-dept-details-count',
      {
        departmentId: departments || [],
        schemeName:  [],
        integrationType: integrationType || []
      },
      { enabled: departments.length > 0 && integrationType.length > 0 }
    )
    

  //@ts-ignore
    const tableData = batchSummary?.data?.analysisData?.map((item: any, index: number) => ({
      id: index + 1,
      department: item.department,
      totalSchemeCount: item.totalSchemeCount,
      totalCandidate: item.totalCandidate,
      totalTpCount: item.totalTpCount,
      totalTcCount: item.totalTcCount,
      totalTrainer: item.totalTrainer ,
      departmentShortName: item.departmentShortName
    }));
    




    return (
      isLoading ? <div className="flex justify-center items-center h-screen">
          <Loader className="animate-spin"  size={50}/>
      </div> :
      <div className="min-h-screen flex flex-col font-[Poppins]">
      
        {/* Content */}
        <section className="flex-grow">
          {/* Search & Filter */}
          <DashFilter/>
        
          {/* Analytics */}

          
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4">
              <div className="grid gap-3 lg:gap-6">
                {/* Stats Cards */}
                <div className="">
                  <CardsCount 
                  
                  />
                </div>

                {/* Charts Section */}
                <div className="grid lg:grid-cols-3 gap-3 lg:gap-6 items-stretch">
                
                  <div className="lg:col-span-2 shadow-md bg-white flex flex-col">
                    {/* @ts-ignore */}
                    <DemoChart  
                      maleData={maleData}
                      femaleData={femaleData}
                      trans={trans}
                      notS={notSData}
                    /> 
                  </div>

                  {/* Skill Training Chart Placeholder */}
                  <div className="shadow-md bg-white flex flex-col">
                    <div className="flex-grow flex flex-col">
                      <PieCharts
                        data={[
                          { name: "Enrolled In Batch", value: enrolledInBatch[0] || 98, fill: "#8ab8f5" },
                          { name: "Enrolled Without Batch", value: enrolledWithoutBatch[0] || 57, fill: "#f87171" }
                        ]}
                        config={skillTrainingConfig}
                        title="Skill Training Candidates:In Batch vs Without Batch"
                        dataKey="value"
                        nameKey="name"
                        showTrend={true}
                        trendValue={10}
                        footerText=""
                        maxHeight={250}
                        className="w-full h-full"
                        innerRadius={60}
              
                      />
                      <div className="mt-6">
                        <ul className="divide-y">  
                          <li className="p-4">
                            <div className="flex gap-4 items-center justify-between">
                              <div className="flex items-center gap-1">
                                <i className="bi bi-circle-fill text-[.55rem] text-sky-600"></i>
                                <span className="text-sm font-bold text-[#8ab8f5]"> Enrolled In Batch</span>
                              </div>
                              <span className="text-xs text-[#8ab8f5]">{enrolledInBatch}</span>
                            </div>
                          </li>
                          <li className="p-4">
                            <div className="flex gap-4 items-center justify-between">
                              <div className="flex items-center gap-1">
                                <i className="bi bi-circle-fill text-[.55rem] text-red-400"></i>
                                <span className="text-sm font-bold text-[#f87171]">Enrolled Without Batch</span>
                              </div>
                              <span className="text-xs  text-[#f87171]">{enrolledWithoutBatch}</span>
                            </div>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Table */}
                <div className="shadow-md bg-white border border-gray-200 overflow-hidden">
                  <div className="flex items-center gap-6 justify-between p-4">
                    <p className="text-sm lg:text-base font-semibold">Department-wise Training Program Analysis</p>
                  
                  </div>
                  
                  <div className="max-h-[20rem] overflow-y-auto">
    <Table columns={columns} data={tableData || []} />
  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  };

  export default Dashboard;