import React, { useEffect, useRef } from "react";

interface FilterDropdownProps {
  label: string;
  options: string[];
  selectedValues: string[];
  onChange: (value: string) => void;
  isOpen: boolean;
  onToggle: () => void;
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  label,
  options,
  selectedValues,
  onChange,
  isOpen,
  onToggle,
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        onToggle(); // closes it if open
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onToggle]);

  

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        className="border rounded-md p-1.5 flex gap-1 text-xs"
        onClick={onToggle}
      >
        <p className="text-gray-600">{label}</p>
      { selectedValues.length > 0 ?  <input
          type="text"
          className="w-16 truncate text-sky-600 font-medium text-left bg-transparent text-xs border-0 p-0 pointer-events-none"
          value={selectedValues}
          readOnly
        /> : <p className="text-blue-600">Select</p>}
        <i className="bi bi-chevron-down text-[.6rem]"></i>
      </button>

      {isOpen && (
        <div className="absolute border rounded-md p-2 text-xs mt-1 top-full left-0 text-left w-full z-10 bg-white shadow-md max-h-96 overflow-y-auto">
          <ul>
            {options.map((option) => (
              <li 
                key={option} 
                className="py-1 px-3 items-center flex gap-1 cursor-pointer"
                
              >
                <input
                  type="checkbox"
                  checked={selectedValues.includes(option)}
                  onChange={() => onChange(option)}
                />
                {option}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default FilterDropdown;
