import React, { useState, useRef } from "react";
import { useOutsideClick } from "@/hooks/useOutsideClick";

interface MultiSelectDropdownProps {
  label: string;
  options: string[];
  selectedOptions: string[];
  onChange: (selected: string[]) => void;
}

export const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
  label,
  options,
  selectedOptions,
  onChange,
}) => {
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  // Close dropdown on outside click
  useOutsideClick(ref as React.RefObject<HTMLElement>, () => setOpen(false));

  const toggleOption = (option: string) => {
    if (selectedOptions.includes(option)) {
      onChange(selectedOptions.filter((o) => o !== option));
    } else {
      onChange([...selectedOptions, option]);
    }
  };

  const toggleAll = () => {
    if (selectedOptions.length === options.length) {
      onChange([]);
    } else {
      onChange(options);
    }
  };

  return (
    <div className="relative" ref={ref}>
      <button
        className="border rounded-md p-1.5 flex gap-1 text-xs items-center"
        onClick={() => setOpen(!open)}
      >
        <p className="text-gray-600">{label}</p>
        <span className="w-16 truncate text-sky-600 font-medium text-left text-xs">
          {selectedOptions.length > 0 ? selectedOptions.join(", ") : "Select"}
        </span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-3 w-3"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {open && (
        <div className="absolute border rounded-md p-2 text-xs mt-1 top-full left-0 text-left min-w-max z-10 bg-white shadow-md max-h-96 overflow-y-auto">
          <ul>
            <li
              className="py-1 px-3 items-center flex gap-1 cursor-pointer"
              onClick={toggleAll}
            >
              <input
                type="checkbox"
                checked={selectedOptions.length === options.length}
                readOnly
              />
              <label>All</label>
            </li>
            {options.map((option) => (
              <li
                key={option}
                className="py-1 px-3 items-center flex gap-1 cursor-pointer"
                onClick={() => toggleOption(option)}
              >
                <input
                  type="checkbox"
                  checked={selectedOptions.includes(option)}
                  readOnly
                />
                <label>{option}</label>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
