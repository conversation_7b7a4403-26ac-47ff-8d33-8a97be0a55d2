import { useFetchQuery } from "@/hooks/useFetchQuery";
import { useState, useEffect } from "react";
import type { DashboardPayload, DashboardStatsResponse } from "@/lib/types";
import { useStoreDashboard } from "@/store/useStoreDashboard";
import { MultiSelectDropdown } from "@/components/filters/multidropdown";

export default function DashFilter() {
  // Use Zustand store for all three
  const { setDepartments, departments, setSchemes, schemes, setIntegrationType, integrationType } = useStoreDashboard();
  const [initialized, setInitialized] = useState(false);

  // Fetch master data
  const { data: masterData } = useFetchQuery<DashboardStatsResponse, DashboardPayload>(
    "masterData",
    import.meta.env.VITE_API_URL + "/master",
    {}
  );
  //@ts-ignore
  const departmentList = masterData?.data?.department?.map((item: any) => item.vsDepartmentName) || [];
  //@ts-ignore
  const schemeList = masterData?.data?.scheme?.map((item: any) => item.schemeName) || [];
  const integrationTypeOptions = [
    { label: "Manual Upload", value: "1" },
    { label: "Bulk Upload(xlsx)", value: "2" },
    { label: "End To End", value: "3" }
  ];


  useEffect(() => {
    if (!initialized && departmentList.length > 0 && departments.length === 0) {
      setDepartments(departmentList);
      setSchemes(schemeList);
      setIntegrationType(integrationTypeOptions.map(opt => opt.value));
      setInitialized(true);
    }
  }, [departmentList, schemeList, integrationTypeOptions, setDepartments, setSchemes, setIntegrationType, initialized, departments.length]);

  console.log("store",useStoreDashboard.getState())

  return (
    <div className="border-b mt-10">
      <div className="max-w-7xl mx-auto px-4 py-2 flex gap-4 items-center flex-wrap">
        <MultiSelectDropdown
          label="Data Source"
          options={integrationTypeOptions.map(opt => opt.label)}
          selectedOptions={integrationTypeOptions
            .filter(opt => integrationType.includes(opt.value))
            .map(opt => opt.label)
          }
          onChange={selectedLabels => {
            // Map selected labels to values before saving to store
            const selectedValues = integrationTypeOptions
              .filter(opt => selectedLabels.includes(opt.label))
              .map(opt => opt.value);
            setIntegrationType(selectedValues);
          }}
        />
        <MultiSelectDropdown
          label="Department"
          options={departmentList}
          selectedOptions={departments}
          onChange={setDepartments}
        />
        <MultiSelectDropdown
          label="Scheme"
          options={schemeList}
          selectedOptions={schemes}
          onChange={setSchemes}
        />
      </div>
    </div>
  );
}
