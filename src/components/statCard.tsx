import { Circle } from "lucide-react";

const StatCard: React.FC<{ 
    title: string; 
    stats: Array<{ label: string; value: string; color: string }>;
    children?: React.ReactNode;
  }> = ({ title, stats, children }) => (
    <div className="shadow-xl bg-white border border-gray-500 flex flex-col">
      <p className="text-sm lg:text-base font-semibold p-4 pb-0 mb-6">{title}</p>
      <div className="flex-grow flex flex-col">
        {children && (
          <div className="flex-grow relative px-4">
            {children}
          </div>
        )}
        <div className="mt-6">
          <ul className="divide-y">
            {stats.map((stat, index) => (
              <li key={index} className="p-4">
                <div className="flex gap-4 items-center justify-between">
                  <div className="flex items-center gap-1">
                    <Circle className={`w-2 h-2 fill-current ${stat.color}`} />
                    <span className="text-xs">{stat.label}</span>
                  </div>
                  <span className="text-xs text-gray-600">{stat.value}</span>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );

  export default StatCard;