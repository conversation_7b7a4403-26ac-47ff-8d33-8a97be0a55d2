
import { Grid, Layers, Square, Backpack, PersonStanding, Lock, Gauge, Briefcase, Goal,  } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useFetchQuery } from '@/hooks/useFetchQuery';
import {type cardsCountPayload, type DashboardStatsResponse, type CardItem } from '@/lib/types';
import { useStoreDashboard } from '@/store/useStoreDashboard';
export default function CardsCount() {
  const departments = useStoreDashboard(state => state.departments); 

  const cardsCountQuery = (payload: cardsCountPayload   ) => {
  const url = import.meta.env.VITE_API_URL + '/dashboard/home'
    return useFetchQuery<DashboardStatsResponse, cardsCountPayload>(
    'dashboardCount',
    url,
    payload
  );
};



const payload: cardsCountPayload ={
  departmentId:departments,
  schemeName:[],
  integrationType: []
} 

const { data: cardsCount } = cardsCountQuery(payload);



  const cards: CardItem[] = [
    { title: "Summary Report", count: cardsCount?.data?.count?.summaryReport?.toLocaleString() , icon: <Lock className="text-sky-600" size={20} />, to: "/summary-report" },
    { title: "Training Sectors", count: cardsCount?.data?.count?.sectors?.toLocaleString() , icon: <Grid className="text-sky-600" size={20} />, to: "/sectors" },
    { title: "Training Courses", count: cardsCount?.data?.count?.courses?.toLocaleString() , icon: <Layers className="text-sky-600" size={20} />, to: "/courses" },
    { title: "Training Partner", count: cardsCount?.data?.count?.tps?.toLocaleString() , icon: <PersonStanding className="text-sky-600" size={20} />, to: "/training-partners" },
    { title: "Training Center", count: cardsCount?.data?.count?.tcs?.toLocaleString() , icon: <Goal className="text-sky-600" size={20} />, to: "/training-centers" },
    { title: "Training Batches", count: cardsCount?.data?.count?.batchs?.toLocaleString() , icon: <Square className="text-sky-600" size={20} />, to: "/batches" },
    { title: "Candidates", count: cardsCount?.data?.count?.candidates?.toLocaleString() , icon: <Backpack className="text-sky-600" size={20} />, to: "/candidates" },
    { title: "Trainers", count: cardsCount?.data?.count?.trainers?.toLocaleString() , icon: <PersonStanding className="text-sky-600" size={20} />, to: "/trainers" },
    { title: "Assessments", count: cardsCount?.data?.count?.assessements?.toLocaleString() , icon: <Gauge className="text-sky-600" size={20} />, to: "/assessment" },
    { title: "Placements", count: cardsCount?.data?.count?.placements?.toLocaleString() , icon: <Briefcase className="text-sky-600" size={20} />, to: "/placement" }
  ];
  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto w-full">
        <div className="grid gap-3 lg:gap-6">
          
          <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-3 lg:gap-6">
            {cards.map((card, index) => (
              <Link
                key={index} 
                to={card.to} 
                className="shadow-md bg-white p-4 border border-gray-200 flex gap-4"
              >
                <div className="flex-shrink-0 w-8 h-8 lg:w-10 lg:h-10 flex items-center justify-center bg-sky-100 rounded-full">
                  {card.icon}
                </div>
                <div>
                  <p className="text-[.65rem] lg:text-xs text-gray-500 font-medium">{card.title}</p>
                  <p className="text-lg lg:text-2xl font-semibold mt-1">{card.count}</p>
                </div>
              </Link>
            ))}
          </div>
          
        </div>
      </div>
    </div>
  );
}