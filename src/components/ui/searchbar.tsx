import React, { memo } from 'react';

interface SearchBarProps {
    text: string;
    value: string;
    onChange: (value: string) => void;
    className?: string;
    isSearching?: boolean;
}

const SearchBar: React.FC<SearchBarProps> = memo(({ 
    text, 
    value, 
    onChange, 
    className = "",
    isSearching = false 
}) => {
    return (
        <div className={`relative ${className} `}>
            <input 
                type="text" 
                placeholder={text}
                value={value}
                onChange={(e) => onChange(e.target.value)}
                className="pl-8 pr-4 py-1.5 w-72 border rounded-md text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            {isSearching ? (
                <div className="absolute left-2.5 top-[50%] -translate-y-[50%] text-gray-400">
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-400"></div>
                </div>
            ) : (
                <i className="bi bi-search absolute left-2.5 top-2 text-gray-400 text-xs flex items-center justify-center"></i>
            )}
        </div>
    );
});

SearchBar.displayName = 'SearchBar';

export default SearchBar;