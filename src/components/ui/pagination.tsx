interface TablePaginationSimpleProps {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    itemsPerPage: number;
    onPageChange: (page: number) => void;
}

export function TablePaginationSimple({
    currentPage,
    totalPages,
    totalRecords,
    itemsPerPage,
    onPageChange
}: TablePaginationSimpleProps) {
    return (
        <div className="flex items-center justify-between gap-4 mt-2">
            <div>
                <p className="text-xs text-blue-600">
                    Showing {(currentPage - 1) * itemsPerPage + 1} - {Math.min(currentPage * itemsPerPage, totalRecords)} of {totalRecords} records
                </p>
            </div>

            <div className="border rounded-md divide-x flex text-xs">
                {/* Previous Button */}
                <button
                    onClick={() => onPageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={`py-1.5 px-3 block ${currentPage === 1 ? "pointer-events-none cursor-not-allowed bg-gray-200" : ""}`}
                >
                    <i className="bi bi-chevron-left text-xs"></i>
                </button>

                {/* Page Numbers */}
                {Array.from({ length: totalPages }, (_, i) => {
                    if (i === 0 || i === totalPages - 1 || Math.abs(currentPage - (i + 1)) <= 2) {
                        return (
                            <button
                                key={i}
                                onClick={() => onPageChange(i + 1)}
                                className={`py-1.5 px-3 cursor-pointer block ${currentPage === i + 1 ? "bg-sky-100 text-sky-600 font-bold" : ""}`}
                            >
                                {i + 1}
                            </button>
                        );
                    } else if (Math.abs(currentPage - (i + 1)) === 3) {
                        return <span key={i} className="py-1.5 px-3 block pointer-events-none">...</span>;
                    }
                    return null;
                })}

                {/* Next Button */}
                <button
                    onClick={() => onPageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className={`py-1.5 px-3 block ${currentPage === totalPages ? "pointer-events-none cursor-not-allowed bg-gray-200" : ""}`}
                >
                    <i className="bi bi-chevron-right text-xs"></i>
                </button>
            </div>
        </div>
    );
}