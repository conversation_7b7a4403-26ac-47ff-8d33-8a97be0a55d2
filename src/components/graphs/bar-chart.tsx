import React, { useRef, useEffect } from 'react';
import {
  Chart,
  BarController,
  BarElement,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend,
} from 'chart.js';

Chart.register(BarController, BarElement, CategoryScale, LinearScale, Tooltip, Legend);

interface Series {
  label: string;
  data: number[];
  backgroundColor: string | string[];
}

interface GroupedBarChartProps {
  id: string;
  labels: string[];
  series: Series[];
  axis:  'x' | 'y';
  height?: string;
}

export const GroupedBarChart: React.FC<GroupedBarChartProps> = ({ id, labels, series, axis, height = '300px' }) => {
  const chartRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = new Chart(chartRef.current, {
      type: 'bar',
      data: {
        labels,
        datasets: series.map((s) => ({
          label: s.label,
          data: s.data,
          backgroundColor: s.backgroundColor,
          borderWidth: 0,
        })),
      },
      options: {
        indexAxis: axis,
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            stacked: true,
            grid: { display: false },
            ticks: {
              color: '#4B5563',
              font: { family: 'Poppins, sans-serif', size: 12 },
              callback: (value, index) =>
                axis === 'x'
                  ? typeof labels[index] === 'string' && labels[index].length > 10
                    ? `${labels[index].slice(0, 10)}…`
                    : labels[index]
                  : value,
            },
          },
          y: {
            stacked: true,
            beginAtZero: true,
            grid: { display: false },
            ticks: {
              color: '#4B5563',
              font: { family: 'Poppins, sans-serif', size: 12 },
              callback: (value, index) =>
                axis === 'y'
                  ? typeof labels[index] === 'string' && labels[index].length > 10
                    ? `${labels[index].slice(0, 10)}…`
                    : labels[index]
                  : value,
            },
          },
        },
        plugins: {
          legend: {
            display: true,
            align: 'start',
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 10,
              boxHeight: 7,
            },
          },
          tooltip: {
            backgroundColor: '#fff',
            borderColor: '#ccc',
            borderWidth: 1,
            displayColors: false,
            titleFont: { size: 14, weight: 500, family: 'Poppins, sans-serif' },
            bodyFont: { size: 12, family: 'Poppins, sans-serif' },
            titleColor: '#000',
            bodyColor: '#666',
            callbacks: {
              title: (ctx) => `Candidates in the ${ctx[0].label} group`,
              label: (ctx) => `${ctx.dataset.label}: ${ctx.raw} Candidates`,
            },
          },
        },
      },
    });

    return () => chart.destroy();
  }, [labels, series, axis]);

  return <canvas id={id} ref={chartRef} style={{ height }} />;
};
