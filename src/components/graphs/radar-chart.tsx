"use client";

import { <PERSON><PERSON>ngle<PERSON>xis, PolarGrid, Radar, RadarChart } from "recharts";

import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
} from "@/components/ui/card";

import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

// Chart config (single dataset with one color)
const chartConfig = {
  caste: {
    label: "Caste Distribution",
    color: "var(--chart-1)",
  },
} satisfies ChartConfig;

export function ChartRadarDots({ data }: { data: { label: string; value: number }[] }) {
  // Transform incoming data to recharts format
  const chartData = data.map((item) => ({
    category: item.label,
    caste: item.value,
  }));

  return (
    <Card>
      <CardHeader className="items-center">
        Caste Distribution
      </CardHeader>
      <CardContent className="pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[250px]"
        >
          <RadarChart data={chartData}>
            <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
            <PolarAngleAxis dataKey="category" />
            <PolarGrid />
            <Radar
              dataKey="caste"
              fill="var(--color-caste)"
              fillOpacity={0.6}
              dot={{ r: 4, fillOpacity: 1 }}
            />
          </RadarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
