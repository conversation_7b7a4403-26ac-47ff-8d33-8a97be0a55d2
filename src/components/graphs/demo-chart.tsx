//@ts-nocheck

import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

interface LineChartProps {
  maleData: number[];
  femaleData: number[];
  trans: number[];
  notS: number[];
}

export const DemoChart: React.FC<LineChartProps> = ({
  maleData,
  femaleData,
  trans,
  notS,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const ctx = canvasRef.current.getContext('2d');
    if (!ctx) return;

    const currentYear = new Date().getFullYear();
    const years = Array.from(
      { length: maleData.length },
      (_, i) => currentYear - (maleData.length - 1 - i)
    );
    const labels = years.map((year) => year.toString());

    const chart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels,
        datasets: [
          {
            label: 'Male',
            data: maleData,
            backgroundColor: 'rgba(2, 147, 215, 0.7)',
          },
          {
            label: 'Female',
            data: femaleData,
            backgroundColor: 'rgba(244, 114, 182, 0.7)',
          },
          {
            label: 'Transgender', 
            data: trans,
            backgroundColor: 'rgba(132, 204, 22, 0.7)',
          },
          {
            label: 'Not Specified',
            data: notS,
            backgroundColor: 'rgba(209, 213, 219, 0.7)',
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            stacked: false,
            grid: { display: false },
            ticks: {
              maxRotation: 0,
            },
            categoryPercentage: 0.6,
            barPercentage: 0.9,
          },
          
          y: {
            stacked: false,
            beginAtZero: true,
            grid: { display: true },
            suggestedMax: Math.max(...maleData, ...femaleData, ...trans, ...notS, 10) * 1.2,
          },
          
        },
        plugins: {
          legend: {
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 10,
              boxHeight: 7,
            },
          },
          tooltip: {
            backgroundColor: '#fff',
            borderColor: '#ccc',
            borderWidth: 1,
            displayColors: true,
            titleFont: {
              size: 14,
              weight: '500',
              family: 'poppins, sans-serif',
            },
            bodyFont: {
              size: 12,
              family: 'poppins, sans-serif',
            },
            titleColor: '#000',
            bodyColor: '#666',
            callbacks: {
              label: function (context) {
                return `${context.dataset.label}: ${context.raw} Candidate`;
              },
            },
          },
        },
      },
    });

    return () => {
      chart.destroy();
    };
  }, [maleData, femaleData, trans, notS]);

  return (
    <div className="w-full h-96 border rounded-lg py-4">
      <h2 className="text-lg font-semibold mb-2 ml-2">
        Annual Candidate Distribution by Gender
      </h2>
      <canvas ref={canvasRef}></canvas>
    </div>
  );
};
