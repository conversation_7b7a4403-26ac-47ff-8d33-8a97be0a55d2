
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>xis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Label<PERSON>ist, ResponsiveContainer } from 'recharts';

import {
  Card,
  CardContent,

  CardHeader,

} from "@/components/ui/card"
import {
  type ChartConfig,
  ChartContainer,

  ChartTooltipContent,
} from "@/components/ui/chart"

export const description = "An interactive bar chart"

export function   ChartBarInteractive({data}:{data:any}) {
 
  const chartConfig = {
    tcCount: {
      label: "Training Centers",
      color: "var(--chart-1)",
    },
  } satisfies ChartConfig

  return (
    <Card>
      <CardHeader>
      
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="aspect-auto h-[300px] w-full">
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 30, bottom: 30 }}
          >
            <XAxis
              dataKey="name"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              interval={0}
              minTickGap={0}
            />
           
            <Tooltip
              content={
                <ChartTooltipContent
                  nameKey="name"
                  labelFormatter={(value) => value}
                />
              }
            />
            <Bar dataKey="value" fill="#0085C3">
              <LabelList dataKey="value" position="top" />
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

