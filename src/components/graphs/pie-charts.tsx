
import { <PERSON>, <PERSON><PERSON><PERSON> } from "recharts"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import type { ReusablePieChartProps } from "@/lib/types"

export default function PieCharts({
  data,
  config,
  title,
  description,
  dataKey,
  nameKey,
  innerRadius = 0,
  className = "",
  showTrend = false,
  footerText,
  maxHeight = 250,

}: ReusablePieChartProps) {



  return (
    <Card className={`flex flex-col ${className}`}>
      <CardHeader className="items-center pb-0 truncate">
        <CardTitle >{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer config={config} className="mx-auto aspect-square" style={{ maxHeight: `${maxHeight}px` }}>
          <PieChart>
            <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
            <Pie data={data} dataKey={dataKey} nameKey={nameKey} innerRadius={innerRadius}  />
          </PieChart>
        </ChartContainer>
      </CardContent>
      {(showTrend || footerText) && (
        <CardFooter className="flex-col gap-2 text-sm">
          {showTrend && (
            <div className="flex items-center gap-2 font-medium leading-none">
               
            </div>
          )}
          {footerText && <div className="leading-none text-muted-foreground">{footerText}</div>}
        </CardFooter>
      )}
    </Card>
  )
}
