import React from "react";
import { Ban } from "lucide-react";

class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean, error: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: new Error() };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, info: React.ErrorInfo) {
    console.error("Error caught by ErrorBoundary:", error, info);
  }

  render() {
    if (this.state.hasError) {
      return <div className="text-red-500 text-center mt-10 text-xl font-semibold   flex flex-col items-center justify-center gap-4 ">
        <h2>Something went wrong. {this.state.error.message}  </h2>

        <Ban className="w-10 h-10 text-red-500" />

      </div>
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
