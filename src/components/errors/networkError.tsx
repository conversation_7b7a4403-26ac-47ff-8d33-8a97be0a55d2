import { useOnline } from '@uiw/react-use-online';
import { WifiOff, RefreshCw } from 'lucide-react';

export const NetworkError = () => {
  const isOnline = useOnline();
  
  if (isOnline) return null;
  
  const handleRetry = () => {
    window.location.reload();
  };
  
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center text-center px-4">
      {/* Icon Container */}
      <div className="mb-8">
        <WifiOff className="w-24 h-24 text-red-500 mx-auto mb-4" />
      </div>
      
      {/* Error Message */}
      <h1 className="text-5xl font-bold text-gray-800 mb-6">
        No Internet Connection
      </h1>
      <p className="text-xl text-gray-600 mb-12 max-w-2xl leading-relaxed">
        It looks like you're offline. Please check your internet connection and try again.
      </p>
      
      {/* Retry Button */}
      <button
        onClick={handleRetry}
        className="inline-flex items-center gap-3 bg-red-500 hover:bg-red-600 text-white font-semibold px-8 py-4 rounded-xl text-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-red-300 shadow-lg"
      >
        <RefreshCw className="w-6 h-6" />
        Try Again
      </button>
      
      {/* Connection Status Indicator */}
      <div className="mt-16">
        <div className="flex items-center justify-center gap-3">
          <div className="w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
          <span className="text-lg text-gray-500 font-medium">Offline</span>
        </div>
      </div>
      
      {/* Troubleshooting Tips */}
      <div className="mt-20 max-w-3xl">
        <h3 className="text-2xl font-semibold text-gray-800 mb-8">Quick fixes:</h3>
        <div className="grid md:grid-cols-3 gap-8 text-gray-600">
          <div className="text-center">
          
            <p className="text-lg">Check your WiFi or mobile data connection</p>
          </div>
          <div className="text-center">
           
            <p className="text-lg">Try moving to a location with better signal</p>
          </div>
          <div className="text-center">
         
            <p className="text-lg">Restart your router or modem</p>
          </div>
        </div>
      </div>
    </div>
  );
};