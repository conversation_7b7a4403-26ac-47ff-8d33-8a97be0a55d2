

const Navbar = () => {
    return (
        <section>
            <div className="max-w-7xl mx-auto flex gap-4 items-center justify-between w-full">
                {/* Logo & Name */}
                <div className="flex gap-2 items-center py-2">
                    <img
                        src="https://convergence-upload.skillmissionassam.org/assets/ASDMLOGO-8Dx_R-8Y.png"
                        alt=""
                        className="h-24"
                    />
                    <div>
                        <h1 className="text-3xl font-bold ">Convergence Dashboard </h1>

                    </div>
                </div>
                {/* Options */}
                <div className="flex gap-2">
                    {/* Current Date */}
                    <div className="border rounded-md p-2 flex gap-2 text-xs">
                        <i className="bi bi-calendar-week"></i>
                        <p className="text-gray-600">
                            {new Intl.DateTimeFormat('en-US', { month: 'short', day: '2-digit', year: 'numeric' }).format(new Date())}
                        </p>
                    </div>
                    {/* Theme Changer */}
                    {/* <label htmlFor="themeChanger" className="border rounded-md p-2 flex gap-2 text-xs cursor-pointer">
                        <input type="checkbox" className="peer/theme hidden" id="themeChanger" />
                        <i className="bi bi-brightness-high peer-checked/theme:hidden"></i>
                        <i className="bi bi-moon hidden peer-checked/theme:block"></i>
                    </label> */}
                    {/* User */}

                </div>
            </div>
        </section>
    );
};

export default Navbar;