import { Link, useParams } from "react-router-dom";


import Table from "@/components/table/table";
import { TrainingPartnersColumns } from "@/lib/tableColumns";
import { useFetchQuery } from "@/hooks/useFetchQuery";
import type { TrainingPartnerFilterState } from "@/lib/types";
import { TablePaginationSimple } from "@/components/ui/pagination";
import { useStoreDashboard } from "@/store/useStoreDashboard";
import { useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import SearchBar from "@/components/ui/searchbar";
import { useDebounce } from "@/hooks/useDebounce";
import { Download, Loader } from "lucide-react";
import FilterDropdown from "@/components/filters/page-filters";
import { useDropDown } from '@/hooks/useDropDown';
export function TrainingPartnerDetailsView() {

  const [search, setSearch] = useState('');
   const debouncedSearch = useDebounce(search, 800)
   const { openDropdowns, toggleDropdown } = useDropDown() ;
  const [department,setDepartment] = useState<string[]>([]);
  const handleSearch = (value: string) => {
    setSearch(value);
  };
  const { id } = useParams();
  console.log(id);
  const [page, setPage] = useState(1);
  const trainingPartnerQuery = (payload: TrainingPartnerFilterState) => {
    const url = import.meta.env.VITE_API_URL + '/cardWise/card-data-list'
    return useFetchQuery<TrainingPartnerFilterState[], TrainingPartnerFilterState>(
      'trainingPartner',
      url,
      payload
    );
  };

  const { departments ,integrationType } = useStoreDashboard()
  const handleDepartmentChange = (value: string) => {
    setDepartment((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [...prev, value]
    );  
  };

  const payload: TrainingPartnerFilterState = {
    departmentId: department.length > 0 ? department : departments, 
    card: ['TP'],
    integrationType: integrationType as unknown as number[],
    schemeName: [],
    entryType: [],
    district: [], 
    take: 10,
    skip: (page - 1) * 10,
    search: debouncedSearch
  };

  const { data: trainingPartner, isLoading } = trainingPartnerQuery(payload);
  console.log(trainingPartner);

  const handlePageChange = (page: number) => {
    setPage(page);
    console.log(page);
  }


  return (



    
    <div className="max-w-7xl mx-auto w-full">
       <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Department Wise Training Partner Analysis</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="mt-4 mb-2 flex gap-2">


<SearchBar
  text="Search by Training Partner Name"
  value={search}
  onChange={handleSearch}
/>   

<FilterDropdown
                  label="Department"
                  options={departments}
                  selectedValues={department}
                  onToggle={() => toggleDropdown('department')}
                  onChange={(value) => handleDepartmentChange(value)}
                  isOpen={openDropdowns['department'] ?? false}
                />
         


                <div className="ml-auto"> 
                <button className="bg-sky-600 text-white px-4 py-2 rounded-md flex gap-1 items-center focus:outline-none">
                  <Download className="w-4 h-4" />
                  <span className="text-xs">Download Report</span>
                </button>
                </div>

</div>
<div className ="border-b">
</div>

      <div className="mt-4">
        {/* @ts-ignore */}
        {isLoading ? <div className="flex justify-center items-center h-screen">
          <Loader className="animate-spin" size={48} />
              {/* @ts-ignore */}
        </div> : <Table className="border border-gray-300" data={trainingPartner?.data.map((item:any, index:number) => ({...item, id:index+1})) || []} columns={TrainingPartnersColumns} />}
        {/* @ts-ignore */}
        <TablePaginationSimple currentPage={page} totalPages={trainingPartner?.pages || 0} totalRecords={trainingPartner?.total || 0} itemsPerPage={20} onPageChange={
          handlePageChange
        } />
      </div>
    </div>

  )
}

export default TrainingPartnerDetailsView;
