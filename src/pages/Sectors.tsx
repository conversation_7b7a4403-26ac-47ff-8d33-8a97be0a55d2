import Table from "@/components/table/table"
import { useFetchQuery } from "@/hooks/useFetchQuery";
import { sectorColumns } from "@/lib/tableColumns"
import { useStoreDashboard } from "@/store/useStoreDashboard";
import { useDebounce } from "@/hooks/useDebounce";
import { useState } from "react";
import SearchBar from "@/components/ui/searchbar";
import { Download, Loader, } from "lucide-react";
import { TablePaginationSimple } from "@/components/ui/pagination";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Link } from "react-router-dom";
import FilterDropdown from "@/components/filters/page-filters";

const Sectors = () => {

  const { departments, integrationType } = useStoreDashboard();
  const [department ,setDepartment] = useState<string[]>([]);
  const [search, setSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage,] = useState<number>(10);
  const debouncedSearch = useDebounce(search, 800)
  const [isOpen, setIsOpen] = useState(false);
  const handleDepartmentChange = (value: string) => {
    setDepartment((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [...prev, value]
    );  
  };
  
  const handleSearch = (value: string) => {
    setSearch(value);
  };
  const payload = {
    departmentId:  department.length > 0 ? department : departments, 
    schemeName: [],
    card: 'sectors',
    integrationType: integrationType as unknown as number[],
    page: currentPage,
    take: itemsPerPage,
    skip: (currentPage - 1),

    search: debouncedSearch,
  }

  const sectorQuery = (payload: any) => {
    const url = import.meta.env.VITE_API_URL + '/cardWise/card-data-list'
    return useFetchQuery<any, any>(
      'sectors',
      url,
      payload
    );
  };

  const { data: sectorData, isLoading } = sectorQuery(payload);
  console.log("sectorData ....", sectorData);
  return (

    <div className='max-w-7xl mx-auto w-full'>
      <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Sectors List</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      <div className="mt-4 mb-4 flex gap-2">


        <SearchBar
          text="Search by Sector Name"
          value={search}
          onChange={handleSearch}
        /> 
        <FilterDropdown
                  label="Department"
                  options={departments}
                  selectedValues={department}
                  onToggle={() => setIsOpen(!isOpen)}
                  onChange={(value) => handleDepartmentChange(value)}
                  isOpen={isOpen}
                />

<div className="ml-auto"> 
                <button className="bg-sky-600 text-white px-4 py-2 rounded-md flex gap-1 items-center focus:outline-none">
                  <Download className="w-4 h-4" />
                  <span className="text-xs">Download Report</span>
                </button>
                </div>
      </div>

      <div className=" border-b mb-4">
        </div>



      {isLoading ? <div className="flex justify-center items-center h-screen">
        <Loader className="animate-spin" size={48} />
      </div> :
        <Table columns={sectorColumns}  data={sectorData?.data.map((item: any, index: number) => ({ ...item, id: index + 1 })) || []} />}
      <TablePaginationSimple
        currentPage={currentPage}
        totalPages={sectorData?.pages || 1}
        totalRecords={sectorData?.total || 0}
        itemsPerPage={itemsPerPage}
        onPageChange={(page) => setCurrentPage(page)}
      />
    </div>

  )
}

export default Sectors