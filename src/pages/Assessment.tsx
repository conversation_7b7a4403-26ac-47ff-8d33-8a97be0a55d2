

import FilterDropdown from "@/components/filters/page-filters";
import Table from "@/components/table/table";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import SearchBar from "@/components/ui/searchbar";
import { useFetchQuery } from "@/hooks/useFetchQuery";
import { assessmentColumns } from "@/lib/tableColumns";
import { useStoreDashboard } from "@/store/useStoreDashboard";
import { useDropDown } from "@/hooks/useDropDown";
import { TablePaginationSimple } from "@/components/ui/pagination";
import { useState } from "react";
import { useDebounce } from "@/hooks/useDebounce";
const Assessment = () => {
  const {departments ,integrationType} = useStoreDashboard();
  const {openDropdowns, toggleDropdown} = useDropDown();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchValue, setSearchValue] = useState('');
  const searchQuery = useDebounce(searchValue, 800)
  const [assessmentStatus,setAssessmentStatus] = useState('')
  const [result,setResult] = useState('');

  const assessmentValue = (value:string) => {
    if(value === 'Pending'){
      return 0
    }else if(value === 'Completed'){
      return 1
    }
  }
  const payload = {
    departmentId:departments || [],
    schemeName :[],
    card:'assessment',
    integrationType : integrationType,
    take:10,
    skip:(currentPage - 1) * 20,
        search:searchQuery,
        assessmentStatus: assessmentValue(assessmentStatus),
    result:result,
  }
  const assessmentQuery = (payload :any) => {
    const url = import.meta.env.VITE_API_URL + '/cardWise/card-data-list'
    return useFetchQuery<any, any>(
      'assessments',
      url,
      payload
    
    );
  };

  const {data :assessmentData} = assessmentQuery(payload);
  const data = assessmentData?.data || [];
  

  
  return (
   <div className="min-h-screen flex flex-col max-w-7xl mx-auto w-full" >
    <Breadcrumb>  
      <BreadcrumbList>  
        <BreadcrumbItem>
          <BreadcrumbLink href="/">Home</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Department Wise Assessment Report</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
    <div className="mt-4 mb-2 flex gap-2 max-w-7xl mx-auto w-full"> 
      <SearchBar
          text="Search by Candidate Name or Tc name..."
          value={searchValue}
          onChange={(e)=>setSearchValue(e)}
        />
        <FilterDropdown
                  label="Assessment Status"
                  options={['Pending', 'Completed']}
                  //@ts-ignore
                    selectedValues={assessmentStatus}
                  onToggle={() => toggleDropdown('assessmentStatus')}
                  onChange={(value) => setAssessmentStatus(value)}
                  isOpen={openDropdowns['assessmentStatus'] ?? false}
                />
                  <FilterDropdown
                  label="Result"
                  options={['Pass', 'Fail', 'Absent', 'Dropout']}
                  //@ts-ignore
                  selectedValues={result}
                  onToggle={() => toggleDropdown('result')}
                  onChange={(value) => setResult(value)}
                  isOpen={openDropdowns['result'] ?? false}
                />
    </div>
    <div className="">
        <Table data={data.map((item: any, index: number) => ({ ...item, id: index + 1 }))} columns={assessmentColumns}/>
        <TablePaginationSimple
          currentPage={currentPage}
          totalPages={assessmentData?.pages}
          totalRecords={assessmentData?.total}
          itemsPerPage={20}
          onPageChange={(page)=>setCurrentPage(page)}
         
        />
    </div>
    </div>
   
   
  )
}

export default Assessment