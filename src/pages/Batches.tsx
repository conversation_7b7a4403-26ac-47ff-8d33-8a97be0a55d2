import { useState } from "react";
import Table from "@/components/table/table";
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink } from "@/components/ui/breadcrumb";
import SearchBar from "@/components/ui/searchbar";
import { useFetchQuery } from "@/hooks/useFetchQuery";
import { batchColumns } from "@/lib/tableColumns";import { useStoreDashboard } from "@/store/useStoreDashboard";
import { ChevronRight, Download, Loader } from "lucide-react";
import { useDebounce } from "@/hooks/useDebounce";
import { TablePaginationSimple } from "@/components/ui/pagination";
import { useDropDown } from "@/hooks/useDropDown";
import FilterDropdown from "@/components/filters/page-filters";
import dayjs from "dayjs";

const Batches = () => {
  const {departments} = useStoreDashboard();
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearch = useDebounce(searchQuery, 800);
  const [currentPage, setCurrentPage] = useState(1);
  const [department,setDepartment] = useState<string[]>([]);
  const [itemsPerPage, ] = useState(10);
  const { openDropdowns, toggleDropdown } = useDropDown() ;
  const payload = {
    departmentId: department.length > 0 ? department : departments, 
    schemeName :[],
    card:'Batch',
    take :itemsPerPage,
    skip : (currentPage - 1) * itemsPerPage,
    search :debouncedSearch,   
    integrationType :[1,2,3],
  }


  const batchQuery = (payload :any) => {
    const url = import.meta.env.VITE_API_URL + '/cardWise/card-data-list'
    return useFetchQuery<any, any>(
      'batches',
      url,
      payload
    );
  };
 

  const {data :batchData , isLoading} = batchQuery(payload);

  const data = batchData?.data || [];

  const handleDepartmentChange = (value: string) => {
    setDepartment((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [...prev, value]
    );  
  };
  return (
    <div>
      <section className="max-w-7xl mx-auto mt-10">
         <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink> 
              <ChevronRight className="w-4 h-4" />
              <BreadcrumbLink href="/">Department Wise Batches Analysis</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
         </Breadcrumb>
      </section> 

      <div className="mt-4 mb-2 flex gap-2 max-w-7xl mx-auto w-full">
        <SearchBar
          text="Search by Tc Name or Course Name or Batch ID ..."
          value={searchQuery}
          onChange={setSearchQuery}
        />

        <FilterDropdown
          label="Department"
          options={departments}
          selectedValues={department}
          onToggle={() => toggleDropdown('department')}
          onChange={(value) => handleDepartmentChange(value)}
          isOpen={openDropdowns['department'] ?? false}
        />

<div className="ml-auto"> 
                <button className="bg-sky-600 text-white px-4 py-2 rounded-md flex gap-1 items-center focus:outline-none">
                  <Download className="w-4 h-4" />
                  <span className="text-xs">Download Report</span>
                </button>
                </div>


      </div>

      <div className="border-b mt-4">
      </div>



      <div className="max-w-7xl mx-auto mt-10"> 
        {isLoading ? <div className="flex justify-center items-center h-screen">
          <Loader className="animate-spin" size={48} />
        </div> :
       <Table
       data={data.map((item: any, index: number) => ({
         ...item,
         id: index + 1,
         batchStartDate: item.batchStartDate
           ? dayjs(item.batchStartDate).format('DD-MM-YYYY')
           : '-',
         batchEndDate: item.batchEndDate
           ? dayjs(item.batchEndDate).format('DD-MM-YYYY')
           : '-',
       }))}
       columns={batchColumns}
     />}
        <TablePaginationSimple
        currentPage={currentPage}
        totalPages={batchData?.pages || 1}
        totalRecords={batchData?.total || 0}
        itemsPerPage={itemsPerPage}
        onPageChange={(page) => setCurrentPage(page)}
      />
      </div>
    </div>
  )
}

export default Batches