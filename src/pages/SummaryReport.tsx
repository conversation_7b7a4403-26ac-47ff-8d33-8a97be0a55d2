import Table from "@/components/table/table"
import SearchBar from "@/components/ui/searchbar";
import { useFetchQuery } from "@/hooks/useFetchQuery";
import { summaryReportColumns } from "@/lib/tableColumns"
import { useStoreDashboard } from "@/store/useStoreDashboard";
import { useState } from "react";
import { useDebounce } from "@/hooks/useDebounce";
import { TablePaginationSimple } from "@/components/ui/pagination";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Link } from "react-router-dom";
import FilterDropdown from "@/components/filters/page-filters";
import { Download } from "lucide-react";
import { useDropDown } from '@/hooks/useDropDown';

const SummaryReport = () => {

  const {departments ,integrationType } = useStoreDashboard();
  const [search, setSearch] = useState('');
  const [fyear,setFyear] = useState('');
  const [department,setDepartment] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const debouncedSearch = useDebounce(search,800)
  const handleSearch = (value: string) => {
    setSearch(value);
  };
  const { openDropdowns, toggleDropdown } = useDropDown() ;
  const [isOpen, setIsOpen] = useState(false);
  const handleDepartmentChange = (value: string) => {
    setDepartment((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [...prev, value]
    );  
  };
  
  const payload = {
    departmentId: department.length > 0 ? department : departments, 
    card:'summaryReport',
    integrationType : integrationType,
    extraMasters :  fyear,
    schemeName :debouncedSearch,
    page:currentPage,
    take:1,
    limit:20,
  } 

  const summaryReportQuery = (payload :any) => {
    const url = import.meta.env.VITE_API_URL + '/cardWise/card-data-list'
    return useFetchQuery<any, any>(
      'summaryReport',
      url,
      payload
    );
  };    

  const handleFilterChange=(value:string)=>{
    //@ts-ignore
    setFyear((prev) =>prev.includes(value) ? prev.filter((item) => item !== value) : [ value])
  }

  const masterDataQuery = () => {
    const url = import.meta.env.VITE_API_URL + '/master'
    return useFetchQuery<any, any>(
      'masterData',
      url,
      {
        departmentId:departments || [],
      integrationType:[1,2,3],
        schemeName:[],
        extraMasters:"fYear"
      }
    );
  };

  const {data :summaryReportData} = summaryReportQuery(payload);
  const data = summaryReportData?.data || [];
  const {data :masterData} = masterDataQuery();
  const financialYear = masterData?.data?.fYear?.map((item:any) => item.dtFinancialYear) || [];

  
  return (
    <div className="max-w-7xl mx-auto mt-10">
       <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Department Wise Summary Report</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

 

<div className="mt-4 mb-4 flex gap-2">
<SearchBar 
        text="Search by Scheme Name"
        value={search}
        onChange={handleSearch}
       /> 

<FilterDropdown
                  label="Department"
                  options={departments}
                  selectedValues={department}
                  onToggle={() => setIsOpen(!isOpen)}
                  onChange={(value) => handleDepartmentChange(value)}
                  isOpen={isOpen}
                />


<FilterDropdown
                 
                 label="Financial Year"
                 options={financialYear}
                 //@ts-ignore
                 selectedValues={fyear}
                 onToggle={() => toggleDropdown('financialYear')}
                 
                 onChange={(value) => handleFilterChange(value)}
                 isOpen={openDropdowns['financialYear'] ?? false}
               />
               

    <div className="ml-auto"> 
                <button className="bg-sky-600 text-white px-4 py-2 rounded-md flex gap-1 items-center focus:outline-none">
                  <Download className="w-4 h-4" />
                  <span className="text-xs">Download Report</span>
                </button>
                </div>
</div>



<div className ="border-b">
</div>
   
     
      <section className="mt-10">
      <Table data={data.map((item:any, index:number) => ({...item, id:index+1}))} columns={summaryReportColumns} />
      </section>

      <TablePaginationSimple
       currentPage={currentPage}
       totalPages={summaryReportData?.pages || 1}
       totalRecords={summaryReportData?.total || 0}
       itemsPerPage={summaryReportData?.limit || 20}
       onPageChange={(page) => setCurrentPage(page)}
      />
    </div>
  )
}

export default SummaryReport