import {Table} from "@/components/table/table"
import { trainerColumns } from "../lib/tableColumns"
import { useStoreDashboard } from "@/store/useStoreDashboard";
import { useFetchQuery } from "@/hooks/useFetchQuery";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import SearchBar from "@/components/ui/searchbar";
import FilterDropdown from "@/components/filters/page-filters";
import { useDropDown } from "@/hooks/useDropDown";
import { TablePaginationSimple } from "@/components/ui/pagination";
import { useState } from "react";
import { useDebounce } from "@/hooks/useDebounce";







const Trainers = () => {

  const {departments ,integrationType} = useStoreDashboard();
  const [department,setDepartment] = useState<string[]>([]);
  const {openDropdowns, toggleDropdown} = useDropDown();
  const [currentPage, setCurrentPage] = useState(1);
  const [search ,setSearch] = useState('');
  const searchQuery = useDebounce(search, 800)
  const handleDepartmentChange = (value: string) => {
    setDepartment((prev) =>
      prev.includes(value)  
        ? prev.filter((item) => item !== value) // remove if already selected
        : [...prev, value]
    );  
  }; 
  const payload = {
    departmentId:department.length > 0 ? department : departments,
    schemeName :[],
    take:10,
    skip:(currentPage - 1) * 10,
    card:'Trainner',
    integrationType : integrationType,
    search:searchQuery
  }

  const trainerQuery = (payload :any) => {
    const url = import.meta.env.VITE_API_URL + '/cardWise/card-data-list'
    return useFetchQuery<any, any>(
      'trainers',
      url,
      payload
    );
  };

  const {data :trainerData} = trainerQuery(payload);
  const data = trainerData?.data || [];

  return (
    <div className="min-h-screen flex flex-col font-sans max-w-7xl mx-auto w-full" >
    <Breadcrumb>  
      <BreadcrumbList>  
        <BreadcrumbItem>
          <BreadcrumbLink href="/">Home</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Department Wise Trainer Report</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>

    <div className="mt-4 mb-2 flex gap-2 max-w-7xl mx-auto w-full">
      <SearchBar
          text="Search by Trainer Name or Course Name or Batch ID ..."
          value={search}
          onChange={(e)=>setSearch(e)}
        />
 <FilterDropdown
                  label="Department"
                  options={departments}
                  selectedValues={department}
                  onToggle={() => toggleDropdown('department')}
                  //@ts-ignore
                  onChange={(value) => handleDepartmentChange(value)}
                  isOpen={openDropdowns['department'] ?? false}
                />    </div>
   



    
    <section>
    <div className="max-w-7xl mx-auto mt-10 ">
        <Table columns={trainerColumns} data={ data.map((item: any, index: number) => ({ ...item, id: index + 1 }))} /> 
        <TablePaginationSimple
              currentPage={currentPage}
              
              totalPages={trainerData?.pages}
       
              totalRecords={trainerData?.total}
              itemsPerPage={20}
              onPageChange={(page) => setCurrentPage(page)}
              />
    </div>

    </section>
    </div>
  )
}

export default Trainers