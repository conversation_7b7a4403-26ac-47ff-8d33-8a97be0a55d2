import Table from "@/components/table/table"
import { useFetchQuery } from "@/hooks/useFetchQuery";
import { coursesColumns } from "@/lib/tableColumns"
import { useStoreDashboard } from "@/store/useStoreDashboard";
import { useDebounce } from "@/hooks/useDebounce";
import { useState } from "react";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Link } from "react-router-dom";
import SearchBar from "@/components/ui/searchbar";
import { Download, Loader } from "lucide-react";
import { TablePaginationSimple } from "@/components/ui/pagination";
import FilterDropdown from "@/components/filters/page-filters";

const Courses = () => {

  const {departments ,integrationType} = useStoreDashboard();
  const [search, setSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage,] = useState<number>(10);
  const debouncedSearch = useDebounce(search, 800)
  const [isOpen, setIsOpen] = useState(false);
  const [department,setDepartment] = useState<string[]>([]);
  const handleDepartmentChange = (value: string) => {
    setDepartment((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [...prev, value]
    );  
  };

  const handleSearch = (value: string) => {
    setSearch(value);
  };
  const payload = {
    departmentId: department.length > 0 ? department : departments, 
    card:['courses'],
    integrationType : integrationType,
    schemeName :[],
    page:currentPage,
    take:itemsPerPage,
    skip:(currentPage-1),
    search:debouncedSearch,
  }

  const courseQuery = (payload :any) => {
    const url = import.meta.env.VITE_API_URL + '/cardWise/card-data-list'
    return useFetchQuery<any, any>(
      'courses',
      url,
      payload
    );
  };
  const {data :courseData, isLoading} = courseQuery(payload);
  console.log("courseData ....", courseData);
  const data = courseData?.data || [];

  return (
    <div>
      <section className="max-w-7xl mx-auto mt-10">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Department Wise  Course  Analysis</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className="mt-4 mb-4 flex gap-2">
          <SearchBar
            text="Search by Course Name or QPNOS Code"
            value={search}
            onChange={handleSearch}
          />
          <FilterDropdown
                  label="Department"
                  options={departments}
                  selectedValues={department}
                  onToggle={() => setIsOpen(!isOpen)}
                  onChange={(value) => handleDepartmentChange(value)}
                  isOpen={isOpen}
                />
                <div className="ml-auto"> 
                <button className="bg-sky-600 text-white px-4 py-2 rounded-md flex gap-1 items-center focus:outline-none">
                  <Download className="w-4 h-4" />
                  <span className="text-xs">Download Report</span>
                </button>
                </div>
                
        </div>
        <div className=" border-b mb-4">
        </div>
        {isLoading ? <div className="flex justify-center items-center h-screen">
          <Loader className="animate-spin" size={48} />
        </div> : <Table data={data.map((item:any, index:number) => ({...item, id:index+1}))} columns={coursesColumns} />}
        <TablePaginationSimple
        currentPage={currentPage}
        totalPages={courseData?.pages || 1}
        totalRecords={courseData?.total || 0}
        itemsPerPage={itemsPerPage}
        onPageChange={(page) => setCurrentPage(page)}
      />
      </section>
    </div>
  )
}

export default Courses