import FilterDropdown from "@/components/filters/page-filters";
import Table from "@/components/table/table";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, B<PERSON><PERSON><PERSON><PERSON>List, BreadcrumbSeparator } from "@/components/ui/breadcrumb";

import SearchBar from "@/components/ui/searchbar";
import { useFetchQuery } from "@/hooks/useFetchQuery";
import { schemeDetailsColumns } from "@/lib/tableColumns";
import { Loader } from "lucide-react";
import { useDropDown } from '@/hooks/useDropDown';
import { Link, useParams } from "react-router-dom";
import { useStoreDashboard } from "@/store/useStoreDashboard";

function SchemeDetails() {
  const { id } = useParams();
  const trimmedId = id?.trim();
   

  //@ts-ignore
  const data = batchSummary?.data?.analysisDataQuery || []
  const { openDropdowns, toggleDropdown } = useDropDown() ;
  const {departments, integrationType} = useStoreDashboard()
  const payload = {
    departmentId: [trimmedId],
    card: 'schemeDetails',
    integrationType: integrationType,
    schemeName: [],
  
  }
  const {data : batchSummary ,isLoading } = useFetchQuery(
    'batchSummary',
    import.meta.env.VITE_API_URL + '/dashboard/all-dept-details-count'+'?departmentId='+trimmedId,
    payload
  )
  return (
    isLoading ? <div className="flex justify-center items-center h-screen">
        <Loader className="animate-spin"  size={50}/>
    </div> :
    <div className="max-w-7xl mt-10 mx-auto" >
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link to="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
      Department wise Scheme Report 
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="mt-4 mb-2 flex gap-2">
        <SearchBar
          text="Search by Scheme Name"
          value={""}
          onChange={()=>{}}
        />
    <FilterDropdown
                 
                 label="Department"
                 options={departments}
                 selectedValues={[]}
                 onToggle={() => toggleDropdown('department')}
                //@ts-ignore
                 onChange={(value) => handleFilterChange('department', value)}
                 isOpen={openDropdowns['department'] ?? false}
               />

      </div>
      <div className="mt-4 mb-2">
        <Table columns={schemeDetailsColumns } data={data.map((item:any, index:number) => ({...item, id:index+1})) } />
        
      </div>
    </div>
  )
}

export default SchemeDetails;