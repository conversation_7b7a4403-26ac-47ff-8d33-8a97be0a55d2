import { placementColumns } from "@/lib/tableColumns"
import { Table } from "@/components/table/table"
import { useStoreDashboard } from "@/store/useStoreDashboard";
import { useFetchQuery } from "@/hooks/useFetchQuery";
import { TablePaginationSimple } from "@/components/ui/pagination";
import { useState } from "react";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import SearchBar from "@/components/ui/searchbar";
import FilterDropdown from "@/components/filters/page-filters";
import { useDropDown } from "@/hooks/useDropDown";

import { Download } from "lucide-react";

const Placement = () => {

  const {departments, integrationType} = useStoreDashboard();
  const {openDropdowns, toggleDropdown} = useDropDown();
  const [currentPage, setCurrentPage] = useState(1);
  const [department,setDepartment] = useState<string[]>([]);
  const handleDepartmentChange = (value: string) => {
    setDepartment((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [...prev, value]
    );  
  }; 
  const payload = {
    departmentId:department.length > 0 ? department : departments,
    schemeName :[],
    card:'placement',
    integrationType : integrationType,
    take:10,
    skip:(currentPage - 1) * 10,
  }

  const placementQuery = (payload :any) => {
    const url = import.meta.env.VITE_API_URL + '/cardWise/card-data-list'
    return useFetchQuery<any, any>(
      'placements',
      url,
      payload
    );
  };

  const {data :placementData} = placementQuery(payload);
  const data = placementData?.data || [];

  return (
    <div className="min-h-screen flex flex-col font-sans max-w-7xl mx-auto w-full" >
      <Breadcrumb>  
      <BreadcrumbList>  
        <BreadcrumbItem>
          <BreadcrumbLink href="/">Home</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Department Wise Placement Report</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
    <div className="mt-4 mb-2 flex gap-2 max-w-7xl mx-auto w-full">
      <SearchBar
          text="Search by Candidate Name ..."
          value={""}
          onChange={()=>{}}
        />
        <FilterDropdown
                  label="Department"
                  options={departments}
                  selectedValues={department}
                  onToggle={() => toggleDropdown('department')}
                  onChange={(value) => handleDepartmentChange(value)}
                  isOpen={openDropdowns['department'] ?? false}
                />
                  <div className="ml-auto"> 
                <button className="bg-sky-600 text-white px-4 py-2 rounded-md flex gap-1 items-center focus:outline-none">
                  <Download className="w-4 h-4" />
                  <span className="text-xs">Download Report</span>
                </button>
                </div>
    </div>
        <div className="">
            <Table columns={placementColumns} data={data.map((item: any, index: number) => ({ ...item, id: index + 1 }))} />
            <TablePaginationSimple
              currentPage={currentPage}
              totalPages={placementData?.pages}
              totalRecords={placementData?.total}
              itemsPerPage={20}
              onPageChange={(page) => setCurrentPage(page)}
            />
        </div>
    </div>
  )
}

export default Placement