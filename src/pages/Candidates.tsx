//@ts-nocheck
import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Search, Download } from 'lucide-react';
import PieCharts from '@/components/graphs/pie-charts';
import { ChartRadarDots } from '@/components/graphs/radar-chart';
import { GroupedBarChart } from '@/components/graphs/bar-chart';
import { AadhaarStatusDistributionConfig, genderDistributionConfig, religionDistributionConfig } from '@/lib/graphconfig';
import FilterDropdown from '@/components/filters/page-filters';
import { useFetchQuery } from '@/hooks/useFetchQuery';
import Table from '@/components/table/table';
import StatCard from '@/components/statCard';
import type { candidateDetailsPayload, candidateDetailsResponse } from '@/lib/types';
import { candidateDetailsColumns } from '@/lib/tableColumns';
import { useDropDown } from '@/hooks/useDropDown';
import { useGetMasterData } from '@/hooks/useGetmasterData';
import { useStoreDashboard } from '@/store/useStoreDashboard';
import { TablePaginationSimple } from '@/components/ui/pagination';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { useDebounce } from '@/hooks/useDebounce';

const Candidates: React.FC = () => {
  const {departments, integrationType} = useStoreDashboard();
  const [currentStep, setCurrentStep] = useState(0)

  const [currentPage, setCurrentPage] = useState(1);
  const [department,setDepartment] = useState<string[]>([]);
  const [search ,setSearch] = useState('');
  const { openDropdowns, toggleDropdown } = useDropDown() ;
    const masterData = useGetMasterData();
    const searchQuery = useDebounce(search, 800)  

     const gender = masterData?.data?.gender?.map(
          (i :any) => i.vsGenderName
     ); 
     
     const qualification = masterData?.data?.qualifications?.map((i)=> i.vsQualification)

   
  const [selectedGender,setGender] = useState<string[]>([]);
  const [selectedQualification,setSelectedQualification] = useState<string[]>([]);
  const [entryType,setEntryType] = useState<string[]>([]);

  const steps = [
    { title: 'Analytics', component: 'analytics' },
    { title: 'Candidates', component: 'candidates' }
  ];

  
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleDepartmentChange = (value: string) => {
    setDepartment((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [...prev, value]
    );  
  }; 

  const handleGenderChange = (value: string) => {
    setGender((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [ value]
    );  
  };

  const handleQualificationChange = (value: string) => {
    setSelectedQualification((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [value]
    );  
  };

  const handleEntryTypeChange = (value: string) => {
    if(value === 'All'){
      setEntryType(" ");
      return;
    }
    setEntryType((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [value]
    );  
  };

  const candidateCount = (payload: candidateDetailsPayload) => {
    return useFetchQuery<candidateDetailsResponse, candidateDetailsPayload>(
      'candidateCount',
      import.meta.env.VITE_API_URL + '/cardWise/card-data-count',  
      payload
    );
  }; 
  
  const {data :candidateCountData ,isLoading } = candidateCount({
    departmentId: department.length > 0 ? department : departments, 
    card:'candidate',
    take:20,
    skip:(currentPage - 1) * 20,
    integrationType :integrationType,
    schemeName :[],
    gender : selectedGender.join(','),
    qualification:selectedQualification.join(','),
    search: searchQuery,
    data_Type: entryType.includes('All') ? '' : entryType.join(',')

  })
  console.log("candidateCountData ....", candidateCountData);
  
  const useCandidateDetails = (payload: candidateDetailsPayload) => {
    return useFetchQuery<candidateDetailsResponse, candidateDetailsPayload>(
      'candidateDetails',
        import.meta.env.VITE_API_URL + '/cardWise/card-data-list',  
      payload
    );
  }; 
  const {data :candidateList  } = useCandidateDetails({
    departmentId: department.length > 0 ? department : departments, 
    take:20,  
    skip:(currentPage - 1) * 20,
    card:'candidate',
    integrationType :integrationType,
    schemeName :[],
    gender:selectedGender.join(','),
    qualification:selectedQualification.join(','),
    search: searchQuery,
    data_Type : entryType.includes('All') ? '' : entryType.join(',')
  })


  const maleCount = candidateCountData?.data?.values?.candidategenderCount.find((i)=> i.vsGender === 'MALE')?.count;
  const femaleCount = candidateCountData?.data?.values?.candidategenderCount.find((i)=> i.vsGender === 'FEMALE')?.count;
  const transgenderCount = candidateCountData?.data?.values?.candidategenderCount.find((i)=> i.vsGender === 'TRANSGENDER')?.count;
  const notSpecifiedCount = candidateCountData?.data?.values?.candidategenderCount.find((i)=> i.vsGender === 'NOT_MENTIONED')?.count;
  const hinduCount = candidateCountData?.data?.values?.candidateReligionCount.find((i)=> i.reg_name === 'Hinduism')?.count;
  const islamCount = candidateCountData?.data?.values?.candidateReligionCount.find((i)=> i.reg_name === 'Islam')?.count;
  const sikhCount = candidateCountData?.data?.values?.candidateReligionCount.find((i)=> i.reg_name === 'Sikhism')?.count; 
 
  const OtherCount = candidateCountData?.data?.values?.candidateReligionCount.find((i)=> i.reg_name === 'Other')?.count;

  const generalCount = candidateCountData?.data?.values?.candidateCategoryCount.find((i)=> i.casteName === 'General')?.count;
  const obcCount = candidateCountData?.data?.values?.candidateCategoryCount.find((i)=> i.casteName === 'Other Backward Caste')?.count;
  const stCount = candidateCountData?.data?.values?.candidateCategoryCount.find((i)=> i.casteName === 'Scheduled Tribe(P)')?.count;
  const stHCount = candidateCountData?.data?.values?.candidateCategoryCount.find((i)=> i.casteName === 'Scheduled Tribe(H)')?.count;
  const mobcCount = candidateCountData?.data?.values?.candidateCategoryCount.find((i)=> i.casteName === 'MOBC')?.count;



  const adhar = candidateCountData?.data?.values?.candidateAadhaarCount.withAadhaar;
  const withoutAdhar = candidateCountData?.data?.values?.candidateAadhaarCount.withOutAadhar;
  const duplicate = candidateCountData?.data?.values?.candidateAadhaarCount.duplicate;

  

 const entryTypeOptions = ['All','duplicate','withOutAdhar']
  return (
    <div className="min-h-screen flex flex-col max-w-7xl mx-auto w-full" >

      <Breadcrumb>  
      <BreadcrumbList>  
        <BreadcrumbItem>
          <BreadcrumbLink href="/">Home</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Department Wise Candidates Analysis</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
      </Breadcrumb>
      {/* Search & Filter Section */}
      <section className="flex-grow">
        <div className="border-b mt-10">
          <div className="max-w-7xl mx-auto px-4 py-2 flex gap-4 items-center justify-between">
            <div className="flex flex-col lg:flex-row gap-2 flex-wrap flex-grow">
              {/* Search */}
              <div className="relative">
                <input
                  type="text"
                  className="pl-8 p-1.5 border border-gray-200 rounded-md focus:border focus:ring-0 text-xs bg-transparent max-w-xs w-full"
                  placeholder="Search for something"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                />
                <Search className="absolute top-1/2 left-2 -translate-y-1/2 text-gray-400 w-4 h-4" />
              </div>
              
              <div className="flex flex-grow gap-2 flex-wrap">
                {/* Filter Dropdowns */}
                <FilterDropdown 
                 
                  label="Entry Type"
                  options={entryTypeOptions}
                  selectedValues={entryType}
                  onToggle={() => toggleDropdown('entryType')}
                  onChange={handleEntryTypeChange}
                  isOpen={openDropdowns['entryType'] ?? false}
                />
                
                <FilterDropdown
                 
                  label="Qualification"
                  options={qualification}
                    selectedValues={selectedQualification}
                  onToggle={() => toggleDropdown('qualification')}
                  onChange={(value) => handleQualificationChange(value)}
                  isOpen={openDropdowns['qualification'] ?? false}
                />
                
                <FilterDropdown
                  label="Gender"
                  options={gender}
                  selectedValues={selectedGender}
                  onToggle={() => toggleDropdown('gender')}
                  onChange={(value) => handleGenderChange(value)}
                  isOpen={openDropdowns['gender'] ?? false}
                />
                <FilterDropdown
                  label="Department"
                  options={departments}
                  selectedValues={department}
                  onToggle={() => toggleDropdown('department')}
                  //@ts-ignore
                  onChange={(value) => handleDepartmentChange(value)}
                  isOpen={openDropdowns['department'] ?? false}
                />
               
                
                {/* Download Button */}
                <div className="ml-auto">
                  <button
                    type="button"
                    className="focus:outline-none text-white bg-sky-600 hover:bg-sky-700 focus:ring-4 focus:ring-sky-300 font-medium rounded-lg text-xs px-5 py-1.5 flex gap-1 items-center"
                  >
                    <Download className="w-4 h-4" />
                    <span>Download Report</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="py-6">
          <div className="max-w-7xl mx-auto px-4">
            {/* Step Navigation */}
            <div className="flex gap-6 items-center justify-between mb-6">
              <div>
                <p className="lg:text-xl font-medium">{steps[currentStep].title}</p>
              </div>
              <div className="flex gap-4">
                <button
                  onClick={prevStep}
                  disabled={currentStep === 0}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-600 font-medium rounded-full border-gray-300 size-6 lg:size-8 flex items-center justify-center text-sm text-center disabled:bg-gray-100 disabled:text-gray-300"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>
                <button
                  onClick={nextStep}
                  disabled={currentStep === steps.length - 1}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-600 font-medium rounded-full border-gray-300 size-6 lg:size-8 flex items-center justify-center text-sm text-center disabled:bg-gray-100 disabled:text-gray-300"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Step Content */}
            {currentStep === 0 && (
              <div className="grid gap-3 lg:gap-6">
                <div className="grid lg:grid-cols-3 gap-3 lg:gap-6 items-stretch">
                  {/* Data Duplicacy Analysis */}
                  <StatCard
                    title="Data duplicacy analysis based on AADHAAR"
                    stats={[
                      { label: 'With AADHAAR', value: adhar, color: 'text-sky-600' },
                      { label: 'Without AADHAAR', value: withoutAdhar, color: 'text-blue-800' },
                      { label: 'Duplicates (AADHAAR based)', value: duplicate, color: 'text-red-600' }
                    ]}
                  >
                    <PieCharts data={[
                      {
                        name: "With AADHAAR",
                        value: adhar,
                        fill: "#8ab8f5"
                      },
                      {
                        name: "Without AADHAAR",
                        value: withoutAdhar,
                        fill: "#0085C3"
                      },
                      
                      {
                        name: "Duplicates(AADHAAR based)",
                        value: duplicate,
                        fill: "#F24C3D"
                      },
                    ]} config={AadhaarStatusDistributionConfig} title="" dataKey="value" nameKey="name" 
                    innerRadius={65}
                    outerRadius={100}
                    className="w-full h-full"
                    />
                  </StatCard>

                  {/* Qualification Levels */}
                  <div className="lg:col-span-2 shadow-md bg-white border border-gray-200 flex flex-col">
                    <p className="text-sm lg:text-base font-semibold p-4 pb-0 mb-6">Qualification Levels by Gender</p>
                    <div className="flex-grow flex flex-col p-4 pt-0">
                      <div className="flex-grow relative">
                            <GroupedBarChart id="bar-chart"
                             labels={['Graduate / Equivalent', 'Higher Secondary', 'High School', '8th Pass', '5th Pass', 'uneducated']} 
                             series={[{ label: 'Male', data: [100, 200, 300, 400, 500, 600], 
                             backgroundColor: 'rgba(2, 132, 199, 1)' },
                              { label: 'Female', data: [50, 150, 250, 350, 450, 550], backgroundColor: 'rgba(244, 114, 182, 1)' }, 
                              { label: 'Transgender', data: [20, 40, 60, 80, 100, 120], backgroundColor: 'rgba(75, 85, 99, 1)' }, 
                              { label: 'Not Specified', data: [10, 20, 30, 40, 50, 60], backgroundColor: 'rgba(209, 213, 219, 1)' },
                            ]} axis="y" />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid lg:grid-cols-3 gap-3 lg:gap-6 items-stretch">
                  {/* Gender Distribution */}
                 
                  <StatCard
                    title="Gender Distribution"
                    stats={[
                      { label: 'Male', value: maleCount, color: 'text-sky-600' },
                      { label: 'Female', value: femaleCount, color: 'text-pink-400' },
                      { label: 'Transgender', value: transgenderCount, color: 'text-gray-600' },
                      { label: 'Not Specified', value: notSpecifiedCount, color: 'text-gray-300' }
                    ]}
                  >
                    <PieCharts data={[
                      {
                        name: "Male",
                        value: maleCount,
                        fill: "#0085C3"
                      },
                      {
                        name: "Female",
                        value: femaleCount,
                        fill: "#8ab8f5"
                      },
                      {
                        name: "Transgender",
                        value: transgenderCount,
                        fill: "#8ab8f5"
                      },
                      {
                        name: "Not Specified",
                        value: notSpecifiedCount,
                        fill: "#F24C3D"
                      },
                    ]} config={genderDistributionConfig} title="" dataKey="value" nameKey="name" 
                    innerRadius={65}
                    outerRadius={100}
                    className="w-full h-full"
                    />
                  </StatCard>

                  {/* Religion Distribution */}
                  <StatCard
                    title="Religion Distribution"
                    stats={[
                      { label: 'Hindu', value: hinduCount, color: 'text-sky-600' },
                      { label: 'Islam', value: islamCount, color: 'text-orange-500' },
                      { label: 'Sikh', value: sikhCount, color: 'text-emerald-500' },
                      { label: 'Others', value: OtherCount, color: 'text-gray-300' }
                    ]}
                  >
                    <PieCharts data={[
                      {
                        name: "Hindu",
                        value: hinduCount,
                        fill: "#8ab8f5"
                      },
                      {
                        name: "Islam",
                        value: islamCount,
                        fill: "#F24C3D"
                      },
                      {
                        name: "Sikh",
                        value: sikhCount,
                        fill: "#6B7280"
                      },
                      {
                        name: "Others", 
                        value: OtherCount,
                        fill: "#7F1D1D" 
                      },
                    ]} config={religionDistributionConfig} title="" dataKey="value" nameKey="name" 
                    innerRadius={65}
                    outerRadius={100}
                    className="w-full h-full"
                    />
                  </StatCard>

                  {/* Category Distribution */}
                  <StatCard
                    title="Category Distribution"
                    stats={[
                      { label: 'General', value: generalCount, color: 'text-sky-600' },
                      { label: 'OBC', value: obcCount, color: 'text-orange-500' },
                      // { label: 'MOBC', value: mobcCount, color: 'text-emerald-500' },
                        { label: 'Scheduled Tribe (Plains)', value: stCount, color: 'text-indigo-600' },
                      { label: 'Scheduled Tribe (Hills)', value: stHCount, color: 'text-indigo-400' }
                    ]}
                  >
                  <ChartRadarDots  
  data={[
    { label: "General", value: generalCount },
    { label: "OBC", value: obcCount },
    { label: "Scheduled Tribe (Plains)", value: stCount },
    { label: "Scheduled Tribe (Hills)", value: stHCount },
    { label: "MOBC", value: mobcCount }
  ]}
/>

                  </StatCard>
                </div>
              </div>
            )}

            {currentStep === 1 && (
              <> 
            <Table
            columns={candidateDetailsColumns}
            // @ts-ignore
            data={Array.isArray(candidateList?.data?.list) ? candidateList?.data?.list : []}
            className="my-6"
            emptyMessage="No candidates found."
          />
              <TablePaginationSimple
              currentPage={currentPage}
              //@ts-ignore
              totalPages={candidateList?.data?.pages}
              //@ts-ignore
              totalRecords={candidateList?.data?.total}
              itemsPerPage={20}
              onPageChange={(page) => setCurrentPage(page)}
              />
              </>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Candidates;