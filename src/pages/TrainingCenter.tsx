
//@ts-nocheck

import React, { useState} from 'react';
import { ChevronLeft, ChevronRight, Download, CircleDot } from 'lucide-react';
import PieCharts from '@/components/graphs/pie-charts';
import { ChartBarInteractive } from '@/components/graphs/tc-bar';
import { duplicateAnalysisConfig } from '@/lib/graphconfig';
import type { TrainingCenter, TrainingCenterFilterState } from '@/lib/types';
import { useFetchQuery } from '@/hooks/useFetchQuery';
import Table from '@/components/table/table';
import { TrainingCentersColumns } from '@/lib/tableColumns';
import { useStoreDashboard } from '@/store/useStoreDashboard';
import SearchBar from '@/components/ui/searchbar';
import FilterDropdown from '@/components/filters/page-filters';
import { useDropDown } from '@/hooks/useDropDown';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Link } from 'react-router-dom';
import { TablePaginationSimple } from '@/components/ui/pagination';
import { MapView } from '@/components/map';

import { useGetMasterData } from '@/hooks/useGetmasterData';
const TrainingCenterDashboard: React.FC = () => {
  const {departments ,integrationType} = useStoreDashboard();
  const [currentStep, setCurrentStep] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [district,setDistrict] = useState<string>('');
  const steps = [
    { title: 'Analytics', component: 'analytics' },
    { title: 'Candidates', component: 'candidates' }
  ];
  const trainingCenterQuery = (payload: TrainingCenterFilterState) => {
    const url = import.meta.env.VITE_API_URL + '/cardWise/card-data-list'  
    return useFetchQuery<TrainingCenter[], TrainingCenterFilterState>(
      'trainingCenter',
      url,
      payload,
   
    );
  };
 
  const masterData = useGetMasterData();
const districts = masterData?.data?.district ?? [];
const districtNames = districts.map((d) => d.vsDistrictName);


  const [department,setDepartment] = useState<string[]>([]);
  const payload: TrainingCenterFilterState = {
    departmentId: department.length > 0 ? department : departments, 
    card: 'TC',
    integrationType: integrationType as unknown as number[],
    schemeName: [], 
    entryType: [],
    search:searchQuery,
    district: district,
    take:10,
    skip:(currentPage - 1) * 10,
  };


  const getTrainingCenterData = ( ) => {
    const url = import.meta.env.VITE_API_URL + '/cardWise/card-data-count'  
    return useFetchQuery<any, TrainingCenterFilterState>(
      'centerList',
      url,
      payload
    );
  };

      

  const { data: trainingCenterData } = trainingCenterQuery(payload);
  const {data : centerData} = getTrainingCenterData()
    

  const tcAddress = centerData?.data?.values?.tcCordinates.map((item:any)=>{
    return {
      latitute: item.vsLatitude,
      longitude: item.vsLongitude,
      name: item.vsAddress,
    }
  })

  const duplicateUnique = centerData?.data?.values?.duplicateUniqueCount.unique;
  const duplicateMultiple = centerData?.data?.values?.duplicateUniqueCount.duplicate;

 
const districtCount = centerData?.data?.values?.distTcCount?.map((item: DistrictCountItem) => ({
  name: item.districtName,
  value: item.total,
}));

const topTC = centerData?.data?.values?.certifiedCandCount?.map((item: CertifiedCandItem) => ({
  name: item.tcName,
  value: item.total,
}));

  

  const { openDropdowns, toggleDropdown } = useDropDown() ;
   
  
  const handleStepChange = (step: number) => {
    if (step >= 0 && step < steps.length) {
      setCurrentStep(step);
    }
  };
 
  const handleDepartmentChange = (value: string) => {
    setDepartment((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [...prev, value]
    );  
  };

  const handleDistrictChange = (value: string) => {
    setDistrict((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value) // remove if already selected
        : [value]
    );  

  };

  const renderAnalytics = () => (
    <div className="grid gap-3 lg:gap-6">
      <div className="grid lg:grid-cols-3 gap-3 lg:gap-6 items-stretch">
        {/* Duplicacy Distribution */}  
        <div className="shadow-md bg-white border border-gray-200 flex flex-col">
          <p className="text-sm lg:text-base font-semibold p-4 pb-0 mb-6">Data duplicacy analysis based on PAN</p>
          <div className="flex-grow flex flex-col">
            <div className="flex-grow relative p-4">
                    <PieCharts data={[
                      {
                        name: "Unique",
                        value:  duplicateUnique,
                        fill: "#0085C3"
                      },
                      {
                        name: "Multiple",
                        value:  duplicateMultiple,
                        fill: "#F24C3D"
                        },
                      ]} config={duplicateAnalysisConfig} title="" dataKey="value" nameKey="name" 
                      innerRadius={65}
                      outerRadius={100}
                      className="w-full h-full"
                    
                      />
            </div>
            <div className="mt-6">
              <ul className="divide-y border-t">
                <li className="p-4">
                  <div className="flex gap-4 items-center justify-between">
                    <div className="flex items-center gap-1">
                      <CircleDot className="w-2 h-2 text-sky-600 fill-current" />
                      <span className="text-xs">Unique</span>
                    </div>
                    <span className="text-xs text-gray-600">{duplicateUnique}</span>
                  </div>
                </li>
                <li className="p-4">
                  <div className="flex gap-4 items-center justify-between">
                    <div className="flex items-center gap-1">
                      <CircleDot className="w-2 h-2 text-red-400 fill-current" />
                      <span className="text-xs">Multiple</span>
                    </div>
                    <span className="text-xs text-gray-600">{duplicateMultiple}</span>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Map */}
        <div className="lg:col-span-2 shadow-md bg-white border border-gray-200 flex flex-col">
          <p className="text-sm lg:text-base font-semibold p-4 pb-0 mb-6">Training Centers Insights & Locations</p>
          <div className="flex-grow flex flex-col p-4">
            <div className="flex-grow relative">
              <MapView locations={tcAddress}/>
            </div>
          </div>
        </div>

        {/* District Distribution */}
        <div className="lg:col-span-2 shadow-md bg-white border border-gray-200 flex flex-col">
          <p className="text-sm lg:text-base font-semibold p-4 pb-0 mb-6 ">Geographical Spread of Top Training Centers by District</p>
          <div className="flex-grow flex flex-col p-4 pt-0">
            <div className="flex-grow relative">
            {centerData?.data?.values?.distTcCount && (
  <ChartBarInteractive data={districtCount} />
)}
            </div>
          </div>
        </div>

        {/* Top Training Centers */}
        <div className="shadow-md bg-white border border-gray-200 flex flex-col">
  <p className="text-sm lg:text-base font-semibold p-4 pb-0 mb-6">
    Top Performing Training Centers by District
  </p>
  <div className="relative overflow-x-auto max-h-[70vh] border-t">
    <table className="w-full text-xs text-left text-gray-500">
      <thead className="text-xs text-gray-500 font-medium uppercase bg-gray-100 sticky top-0 z-10">
        <tr>
          <th className="px-4 py-4 text-center w-6">#</th>
          <th className="px-4 py-4">Name</th>
          <th className="px-4 py-4 text-center w-1/12">Certified</th>
        </tr>
      </thead>

      <tbody>
         {centerData?.data?.values?.certifiedCandCount && 
         topTC.slice(0,6).map((item: any, index: number) => (
          <tr key={index}>
            <td className="px-4 py-4 text-center">{index + 1}</td>
            <td className="px-4 py-4">{item.name}</td>
            <td className="px-4 py-4 text-center">{item.value}</td>
          </tr>
        ))
         }
      </tbody>


    </table>
  </div>
</div>

      </div>
    </div>
  );
  
  const renderCandidates = () => (
    <>
    <Table
    columns={TrainingCentersColumns}
    // @ts-ignore
      data={Array.isArray(trainingCenterData?.data) ? trainingCenterData?.data: []}
    className="my-6"
    emptyMessage="No training centers found."
  />
  <TablePaginationSimple
              currentPage={currentPage}
              //@ts-ignore
              totalPages={trainingCenterData?.pages}
              //@ts-ignore
              totalRecords={trainingCenterData?.total}
              itemsPerPage={20}
              onPageChange={(page) => setCurrentPage(page)}
            />
            </>
  );

  return (
    <div className="min-h-screen flex flex-col font-sans max-w-7xl mx-auto w-full" >

<Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Department Wise Training Center Analysis</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

    

      {/* Content */}
      <section className="flex-grow">
        {/* Search & Filter */}
        <div className="border-b mt-10">
          <div className="max-w-7xl mx-auto px-4 py-2 flex gap-4 items-center justify-between">
            <div className="flex flex-col lg:flex-row gap-2 flex-wrap flex-grow">
              {/* Search */} 

              <SearchBar
              text="Search by Training Center Name or Partner Name"
              value={searchQuery}
              onChange={setSearchQuery}
              /> 

              <FilterDropdown
                  label="Department"
                  options={departments}
                  selectedValues={department}
                  onToggle={() => toggleDropdown('department')}
                  onChange={(value) => handleDepartmentChange(value)}
                  isOpen={openDropdowns['department'] ?? false}
                />

                <FilterDropdown
                  label="District"
                  options={districtNames}
                  selectedValues={district}
                  onToggle={() => toggleDropdown('district')}
                  onChange={(value) => handleDistrictChange(value)}
                  isOpen={openDropdowns['district'] ?? false}
                /> 

                {  
                 currentStep === 1 && ( 
                  <div className="ml-auto"> 
                   <button className="bg-sky-600 text-white px-4 py-2 rounded-md flex gap-1 items-center focus:outline-none">
                    <Download className="w-4 h-4" />
                    <span className="text-xs">Download Report</span>
                   </button>
                   </div>
                 )
                }

         
            </div>
          </div>
        </div>

        {/* Data */}
        <div className="py-6">
          <div className="max-w-7xl mx-auto px-4">
            <div>
              {/* Step Indicators & Navigation */}
              <div className="flex gap-6 items-center justify-between mb-6">
                <div>
                  <p className="lg:text-xl font-medium">{steps[currentStep].title}</p>
                </div>
                <div className="flex gap-4">
                  <button 
                    type="button" 
                    onClick={() => handleStepChange(currentStep - 1)}
                    disabled={currentStep === 0}
                    className="bg-gray-200 hover:bg-gray-300 text-gray-600 font-medium rounded-full border-gray-300 w-6 h-6 lg:w-8 lg:h-8 flex items-center justify-center text-sm disabled:bg-gray-100 disabled:text-gray-300"
                  >
                    <ChevronLeft className="w-3 h-3" />
                  </button>
                  <button 
                    type="button" 
                    onClick={() => handleStepChange(currentStep + 1)}
                    disabled={currentStep === steps.length - 1}
                    className="bg-gray-200 hover:bg-gray-300 text-gray-600 font-medium rounded-full border-gray-300 w-6 h-6 lg:w-8 lg:h-8 flex items-center justify-center text-sm disabled:bg-gray-100 disabled:text-gray-300"
                  >
                    <ChevronRight className="w-3 h-3" />
                  </button>
                </div>
              </div>

              {/* Step Content */}
              <div>
                {currentStep === 0 && renderAnalytics()}
                {currentStep === 1 && renderCandidates()}
              </div>
            </div>
          </div>
        </div>
      </section>

     
    </div>
  );
};

export default TrainingCenterDashboard;