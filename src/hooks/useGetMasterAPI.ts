import { useQuery } from '@tanstack/react-query';

interface Department {
  pklDepartmentId: number;
  vsDepartmentName: string;
}

interface Scheme {
  schemeName: string;
}

interface District {
  pklDistrictId: number;
  vsDistrictName: string;
}

interface State {
  pklStateId: number;
  vsStateName: string;
}

interface MasterData {
  status: string;
  statusCode: number;
  data: {
    department: Department[];
    scheme: Scheme[];
    district?: District[];
    state?: State[];
  };
}

const MASTER_DATA_KEY = ['masterData'];
const fetchMasterData = async (): Promise<MasterData> => {
  const url = import.meta.env.VITE_API_URL + '/master';
  const response = await fetch(url, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      departmentId: [],
      schemeName: []
    })
  });
  if (!response.ok) throw new Error('Failed to fetch master data');
  const data = await response.json();
  localStorage.setItem('masters', JSON.stringify(data));
  return data;
};

export const useMasterDataQuery = () => {
  return useQuery<MasterData, Error>({
    queryKey: MASTER_DATA_KEY,
    queryFn: fetchMasterData,
    staleTime: 1000 * 60 * 60 * 24, 
  });
}

