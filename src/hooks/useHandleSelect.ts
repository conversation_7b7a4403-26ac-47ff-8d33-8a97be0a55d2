type HandleSelectFn = (value: string, isDoubleClick?: boolean) => void;

export const useHandleSelect: HandleSelectFn = (value, isDoubleClick = false) => {
  return (prevSelected: string[]) => {
    if (isDoubleClick) {
      // Uncheck (remove) on double-click
      return prevSelected.filter((v) => v !== value);
    } else {
      // Check (add) on single-click
      return prevSelected.includes(value) ? prevSelected : [...prevSelected, value];
    }
  };
};
