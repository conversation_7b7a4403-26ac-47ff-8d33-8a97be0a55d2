import { useQuery, type UseQueryOptions } from '@tanstack/react-query';
import axios from 'axios';

export const fetchData = async <TResponse, TPayload>(
  url: string,
  payload?: TPayload
): Promise<TResponse> => {
  const response = await axios.post<TResponse>(url, payload);
  return response.data;
};

export const useFetchQuery = <TResponse, TPayload>(
  key: string,
  url: string,
  payload?: TPayload,
  options?: Partial<UseQueryOptions<TResponse>>
) => {
  return useQuery<TResponse>({
    queryKey: [key, payload],
    queryFn: () => fetchData<TResponse, TPayload>(url, payload),
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};
