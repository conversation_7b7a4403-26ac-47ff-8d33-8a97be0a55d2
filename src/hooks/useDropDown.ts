
import { useState, useEffect, useRef } from "react";

export const useDropDown = () => {
  const [openDropdowns, setOpenDropdowns] = useState<{ [key: string]: boolean }>({});
  const dropdownRefs = useRef<{ [key: string]: HTMLElement | null }>({});

  const toggleDropdown = (id: string) => {
    setOpenDropdowns((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const registerRef = (id: string, ref: HTMLElement | null) => {
    dropdownRefs.current[id] = ref;
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const isAnyOpenAndOutside = Object.entries(dropdownRefs.current).some(([id, ref]) => {
        return ref && openDropdowns[id] && !ref.contains(event.target as Node);
      });

      if (isAnyOpenAndOutside) {
        setOpenDropdowns({});
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [openDropdowns]);

  return { openDropdowns, toggleDropdown, registerRef };
};
