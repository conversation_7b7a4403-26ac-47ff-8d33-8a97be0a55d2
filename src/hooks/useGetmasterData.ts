import { useEffect, useState } from "react";
interface Department {
    pklDepartmentId: number;
    vsDepartmentName: string;
  }
  
  interface Scheme {
    schemeName: string;
  }
  
  interface District {
    pklDistrictId: number;
    vsDistrictName: string;
  }
  
  interface State {
    pklStateId: number;
    vsStateName: string;
  }
  
  interface MasterData {
    status: string;
    statusCode: number;
    data: {
      department: Department[];
      scheme: Scheme[];
      district?: District[];
      state?: State[];
    };
  }
  

export function useGetMasterData() {
  const [masterData, setMasterData] = useState<MasterData | null>(null);

  useEffect(() => {
    const masters = localStorage.getItem("masters");
    if (masters) {
      try {
        setMasterData(JSON.parse(masters));
      } catch (e) {
        console.error("Invalid master data in localStorage:", e);
      }
    }
  }, []);

  return masterData;
}
