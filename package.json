{"name": "dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@react-google-maps/api": "^2.20.7", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.81.2", "@uidotdev/usehooks": "^2.4.1", "@uiw/react-use-online": "^1.0.0", "axios": "^1.10.0", "chart.js": "^4.5.0", "chartjs": "^0.3.24", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "google-maps": "^4.3.3", "lucide-react": "^0.523.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "recharts": "^3.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/chart.js": "^2.9.41", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^6.0.0"}}